-- Script to populate departments, assign managers, and staff with proper relationships
-- This script ensures proper department structure and user assignments

-- First, let's create some departments if they don't exist
INSERT INTO departments (id, name, description, manager_id) VALUES 
  (gen_random_uuid(), 'Engineering', 'Software development and technical operations', NULL),
  (gen_random_uuid(), 'Operations', 'Day-to-day business operations and logistics', NULL),
  (gen_random_uuid(), 'Finance', 'Financial planning, accounting, and budget management', NULL),
  (gen_random_uuid(), 'Human Resources', 'Employee management and organizational development', NULL),
  (gen_random_uuid(), 'Construction', 'Construction projects and site management', NULL),
  (gen_random_uuid(), 'Fleet Management', 'Vehicle maintenance and fleet operations', NULL)
ON CONFLICT (name) DO NOTHING;

-- Update existing users or create sample users with proper department assignments
-- Note: This assumes you have some existing users in your profiles table

-- Create sample admin user if doesn't exist
INSERT INTO profiles (id, full_name, email, role, department_id, created_at, updated_at)
SELECT 
  gen_random_uuid(),
  'System Administrator',
  '<EMAIL>',
  'admin',
  NULL, -- <PERSON><PERSON> typically don't belong to specific departments
  NOW(),
  NOW()
WHERE NOT EXISTS (SELECT 1 FROM profiles WHERE role = 'admin' LIMIT 1);

-- Assign managers to departments
DO $$
DECLARE
    engineering_dept_id UUID;
    operations_dept_id UUID;
    finance_dept_id UUID;
    hr_dept_id UUID;
    construction_dept_id UUID;
    fleet_dept_id UUID;
    manager_user_id UUID;
BEGIN
    -- Get department IDs
    SELECT id INTO engineering_dept_id FROM departments WHERE name = 'Engineering' LIMIT 1;
    SELECT id INTO operations_dept_id FROM departments WHERE name = 'Operations' LIMIT 1;
    SELECT id INTO finance_dept_id FROM departments WHERE name = 'Finance' LIMIT 1;
    SELECT id INTO hr_dept_id FROM departments WHERE name = 'Human Resources' LIMIT 1;
    SELECT id INTO construction_dept_id FROM departments WHERE name = 'Construction' LIMIT 1;
    SELECT id INTO fleet_dept_id FROM departments WHERE name = 'Fleet Management' LIMIT 1;

    -- Create or update manager users
    -- Engineering Manager
    INSERT INTO profiles (id, full_name, email, role, department_id, created_at, updated_at)
    VALUES (gen_random_uuid(), 'John Engineering Manager', '<EMAIL>', 'manager', engineering_dept_id, NOW(), NOW())
    ON CONFLICT (email) DO UPDATE SET 
        role = 'manager',
        department_id = engineering_dept_id,
        updated_at = NOW()
    RETURNING id INTO manager_user_id;
    
    UPDATE departments SET manager_id = manager_user_id WHERE id = engineering_dept_id;

    -- Operations Manager  
    INSERT INTO profiles (id, full_name, email, role, department_id, created_at, updated_at)
    VALUES (gen_random_uuid(), 'Sarah Operations Manager', '<EMAIL>', 'manager', operations_dept_id, NOW(), NOW())
    ON CONFLICT (email) DO UPDATE SET 
        role = 'manager',
        department_id = operations_dept_id,
        updated_at = NOW()
    RETURNING id INTO manager_user_id;
    
    UPDATE departments SET manager_id = manager_user_id WHERE id = operations_dept_id;

    -- Finance Manager
    INSERT INTO profiles (id, full_name, email, role, department_id, created_at, updated_at)
    VALUES (gen_random_uuid(), 'Mike Finance Manager', '<EMAIL>', 'manager', finance_dept_id, NOW(), NOW())
    ON CONFLICT (email) DO UPDATE SET 
        role = 'manager',
        department_id = finance_dept_id,
        updated_at = NOW()
    RETURNING id INTO manager_user_id;
    
    UPDATE departments SET manager_id = manager_user_id WHERE id = finance_dept_id;

    -- Construction Manager
    INSERT INTO profiles (id, full_name, email, role, department_id, created_at, updated_at)
    VALUES (gen_random_uuid(), 'David Construction Manager', '<EMAIL>', 'manager', construction_dept_id, NOW(), NOW())
    ON CONFLICT (email) DO UPDATE SET 
        role = 'manager',
        department_id = construction_dept_id,
        updated_at = NOW()
    RETURNING id INTO manager_user_id;
    
    UPDATE departments SET manager_id = manager_user_id WHERE id = construction_dept_id;

    -- Create sample staff users assigned to departments
    -- Engineering Staff
    INSERT INTO profiles (id, full_name, email, role, department_id, created_at, updated_at)
    VALUES 
        (gen_random_uuid(), 'Alice Developer', '<EMAIL>', 'staff', engineering_dept_id, NOW(), NOW()),
        (gen_random_uuid(), 'Bob Developer', '<EMAIL>', 'staff', engineering_dept_id, NOW(), NOW()),
        (gen_random_uuid(), 'Carol QA Engineer', '<EMAIL>', 'staff', engineering_dept_id, NOW(), NOW())
    ON CONFLICT (email) DO UPDATE SET 
        role = 'staff',
        department_id = EXCLUDED.department_id,
        updated_at = NOW();

    -- Operations Staff
    INSERT INTO profiles (id, full_name, email, role, department_id, created_at, updated_at)
    VALUES 
        (gen_random_uuid(), 'Diana Operations', '<EMAIL>', 'staff', operations_dept_id, NOW(), NOW()),
        (gen_random_uuid(), 'Eric Operations', '<EMAIL>', 'staff', operations_dept_id, NOW(), NOW()),
        (gen_random_uuid(), 'Fiona Coordinator', '<EMAIL>', 'staff', operations_dept_id, NOW(), NOW())
    ON CONFLICT (email) DO UPDATE SET 
        role = 'staff',
        department_id = EXCLUDED.department_id,
        updated_at = NOW();

    -- Finance Staff
    INSERT INTO profiles (id, full_name, email, role, department_id, created_at, updated_at)
    VALUES 
        (gen_random_uuid(), 'Grace Accountant', '<EMAIL>', 'accountant', finance_dept_id, NOW(), NOW()),
        (gen_random_uuid(), 'Henry Finance', '<EMAIL>', 'staff', finance_dept_id, NOW(), NOW())
    ON CONFLICT (email) DO UPDATE SET 
        role = EXCLUDED.role,
        department_id = EXCLUDED.department_id,
        updated_at = NOW();

    -- Construction Staff
    INSERT INTO profiles (id, full_name, email, role, department_id, created_at, updated_at)
    VALUES 
        (gen_random_uuid(), 'Ivan Site Manager', '<EMAIL>', 'staff', construction_dept_id, NOW(), NOW()),
        (gen_random_uuid(), 'Judy Foreman', '<EMAIL>', 'staff', construction_dept_id, NOW(), NOW()),
        (gen_random_uuid(), 'Kevin Safety Officer', '<EMAIL>', 'staff', construction_dept_id, NOW(), NOW())
    ON CONFLICT (email) DO UPDATE SET 
        role = 'staff',
        department_id = EXCLUDED.department_id,
        updated_at = NOW();

END $$;

-- Create some sample projects assigned to departments
INSERT INTO projects (id, name, description, status, manager_id, department_id, created_at, updated_at)
SELECT 
    gen_random_uuid(),
    project_name,
    project_desc,
    'active',
    d.manager_id,
    d.id,
    NOW(),
    NOW()
FROM (
    VALUES 
        ('Website Redesign', 'Complete overhaul of company website with modern design'),
        ('Mobile App Development', 'Develop new mobile application for customer portal'),
        ('Database Migration', 'Migrate legacy database to new cloud infrastructure')
) AS projects_data(project_name, project_desc)
CROSS JOIN departments d 
WHERE d.name = 'Engineering'
ON CONFLICT DO NOTHING;

INSERT INTO projects (id, name, description, status, manager_id, department_id, created_at, updated_at)
SELECT 
    gen_random_uuid(),
    project_name,
    project_desc,
    'active',
    d.manager_id,
    d.id,
    NOW(),
    NOW()
FROM (
    VALUES 
        ('Office Renovation', 'Renovation of main office building'),
        ('New Warehouse Construction', 'Construction of additional warehouse facility'),
        ('Safety Protocol Update', 'Update and implement new safety protocols')
) AS projects_data(project_name, project_desc)
CROSS JOIN departments d 
WHERE d.name = 'Construction'
ON CONFLICT DO NOTHING;

-- Create some sample memos for departments
INSERT INTO memos (id, title, content, department_id, created_by, created_at, updated_at)
SELECT 
    gen_random_uuid(),
    memo_title,
    memo_content,
    d.id,
    d.manager_id,
    NOW(),
    NOW()
FROM (
    VALUES 
        ('Engineering Team Meeting', 'Weekly engineering team meeting scheduled for Friday at 2 PM. Please prepare your sprint updates.'),
        ('Code Review Guidelines', 'New code review guidelines have been established. All pull requests must be reviewed by at least two team members.'),
        ('Technology Stack Update', 'We are migrating to the latest version of our framework. Training sessions will be scheduled next week.')
) AS memos_data(memo_title, memo_content)
CROSS JOIN departments d 
WHERE d.name = 'Engineering' AND d.manager_id IS NOT NULL
ON CONFLICT DO NOTHING;

INSERT INTO memos (id, title, content, department_id, created_by, created_at, updated_at)
SELECT 
    gen_random_uuid(),
    memo_title,
    memo_content,
    d.id,
    d.manager_id,
    NOW(),
    NOW()
FROM (
    VALUES 
        ('Site Safety Reminder', 'All personnel must wear appropriate PPE on construction sites. Hard hats and safety vests are mandatory.'),
        ('Equipment Maintenance', 'Scheduled maintenance for all heavy equipment will be performed this weekend. Plan accordingly.'),
        ('Project Timeline Update', 'The warehouse construction project timeline has been updated. New completion date is set for Q2 2024.')
) AS memos_data(memo_title, memo_content)
CROSS JOIN departments d 
WHERE d.name = 'Construction' AND d.manager_id IS NOT NULL
ON CONFLICT DO NOTHING;

-- Update any existing users without department assignments
UPDATE profiles 
SET department_id = (
    SELECT id FROM departments WHERE name = 'Operations' LIMIT 1
)
WHERE department_id IS NULL AND role IN ('staff', 'manager') AND email NOT LIKE '%admin%';

-- Create some sample tasks assigned to department staff
INSERT INTO tasks (id, title, description, status, priority, assigned_to_id, created_by_id, project_id, created_at, updated_at)
SELECT 
    gen_random_uuid(),
    task_title,
    task_desc,
    'pending',
    'medium',
    p.id, -- assigned to staff
    mgr.id, -- created by manager
    proj.id, -- project
    NOW(),
    NOW()
FROM (
    VALUES 
        ('Setup Development Environment', 'Configure local development environment for new project'),
        ('Database Schema Design', 'Design database schema for new customer portal'),
        ('Unit Test Implementation', 'Write comprehensive unit tests for user authentication module')
) AS tasks_data(task_title, task_desc)
CROSS JOIN profiles p
CROSS JOIN profiles mgr
CROSS JOIN projects proj
CROSS JOIN departments d
WHERE p.role = 'staff' 
  AND mgr.role = 'manager' 
  AND p.department_id = mgr.department_id 
  AND proj.department_id = d.id 
  AND d.name = 'Engineering'
  AND mgr.department_id = d.id
LIMIT 3
ON CONFLICT DO NOTHING;

-- Add some system activities for demonstration
INSERT INTO system_activities (id, activity_type, description, user_id, severity, created_at)
SELECT 
    gen_random_uuid(),
    activity_type,
    description,
    p.id,
    'info',
    NOW() - INTERVAL '1 day' * RANDOM() * 7
FROM (
    VALUES 
        ('user_login', 'User logged into the system'),
        ('task_created', 'New task was created and assigned'),
        ('memo_published', 'Department memo was published'),
        ('project_updated', 'Project status was updated'),
        ('department_assigned', 'User was assigned to department')
) AS activities_data(activity_type, description)
CROSS JOIN profiles p
WHERE p.role IN ('staff', 'manager')
LIMIT 20
ON CONFLICT DO NOTHING;

-- Print summary of what was created
DO $$
DECLARE
    dept_count INTEGER;
    user_count INTEGER;
    project_count INTEGER;
    memo_count INTEGER;
    task_count INTEGER;
BEGIN
    SELECT COUNT(*) INTO dept_count FROM departments;
    SELECT COUNT(*) INTO user_count FROM profiles;
    SELECT COUNT(*) INTO project_count FROM projects;
    SELECT COUNT(*) INTO memo_count FROM memos;
    SELECT COUNT(*) INTO task_count FROM tasks;
    
    RAISE NOTICE 'Database population completed:';
    RAISE NOTICE '- Departments: %', dept_count;
    RAISE NOTICE '- Users: %', user_count;
    RAISE NOTICE '- Projects: %', project_count;
    RAISE NOTICE '- Memos: %', memo_count;
    RAISE NOTICE '- Tasks: %', task_count;
END $$; 