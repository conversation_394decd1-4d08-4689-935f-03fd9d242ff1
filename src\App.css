/* ===== CSS Reset & Global Styles ===== */
*,
*::before,
*::after {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  background-color: #f9fafb;
  color: #1f2937;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ===== Root Layout ===== */
#root {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem 1rem;
  text-align: center;
}

/* ===== Logo Animation ===== */
.logo {
  height: 6em;
  padding: 1.5em;
  will-change: filter;
  transition: filter 300ms;
}

.logo:hover {
  filter: drop-shadow(0 0 2em #646cffaa);
}

.logo.react:hover {
  filter: drop-shadow(0 0 2em #61dafbaa);
}

@keyframes logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion: no-preference) {
  a:nth-of-type(2) .logo {
    animation: logo-spin infinite 20s linear;
  }
}

/* ===== Card Utility ===== */
.card {
  background-color: #ffffff;
  padding: 2rem;
  margin: 1rem auto;
  border-radius: 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  max-width: 600px;
}

/* ===== Docs Link Style ===== */
.read-the-docs {
  color: #888;
  font-size: 0.9rem;
}

/* ===== Responsive Adjustments ===== */
@media (max-width: 768px) {
  #root {
    padding: 1rem;
  }

  .logo {
    height: 4em;
  }

  .card {
    padding: 1.5rem;
  }
}
