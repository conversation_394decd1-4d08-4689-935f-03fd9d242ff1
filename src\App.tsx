import { Suspense } from "react";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { AuthProvider } from "@/components/auth/AuthProvider";
import { ProtectedRoute } from "@/components/auth/ProtectedRoute";
import { ErrorBoundary } from "@/components/ErrorBoundary";
import { Loader } from "@/components/ui/Loader";
import { UnifiedDashboardLayout } from "@/components/layout/UnifiedDashboardLayout";
import { lazy } from "react";

// Public Pages
import { AuthPage } from "@/pages/AuthPage";
import ClockInPage from "@/pages/ClockInPage";
import { DebugPage } from "@/pages/DebugPage";

// Dashboard Components - Unified Dashboard
import Dashboard from "@/pages/Dashboard";

// Admin Components
import { UserManagement } from "@/components/admin/UserManagement";
import { DepartmentManagement } from "@/components/admin/DepartmentManagement";
import { ProjectManagement } from "@/components/admin/ProjectManagement";
import { AdminReportsManagement } from "@/components/admin/ReportsManagement";
import { ActivityManagement } from "@/components/admin/ActivityManagement";
import { CommunicationCenter } from "@/components/admin/CommunicationCenter";
import SystemDiagnosticsPage from "@/pages/admin/SystemDiagnosticsPage";

// Manager Components
import { TeamOverview } from "@/components/manager/TeamOverview";
import { InvoiceManagement } from "@/components/manager/InvoiceManagement";
import { ReportManagement } from "@/components/manager/ReportManagement";
import ManagerSettings from "@/pages/manager/ManagerSettings";

// Staff Components
import { MyTasks } from "@/components/staff/MyTasks";
import Memos from "@/pages/staff/Memos";
import Reports from "@/pages/staff/Reports";
import Meetings from "@/pages/staff/Meetings";

// Accountant Components
import { InvoiceManagement as AccountantInvoiceManagement } from "@/components/accountant/InvoiceManagement";

// Staff Admin Components
import ExpenseManagement from "@/components/staff-admin/modules/ExpenseManagement";
import FleetManagement from "@/components/staff-admin/modules/FleetManagement";
import AssetInventoryManagement from "@/components/staff-admin/modules/AssetInventoryManagement";

import AssetsPage from "@/pages/assets/AssetsPage";
import ConstructionPage from "@/pages/construction/ConstructionPage";
import DocumentsPage from "@/pages/documents/DocumentsPage";
import FleetPage from "@/pages/fleet/FleetPage";
import FinancialPage from "@/pages/financial/FinancialPage";
import BatteryPage from "@/pages/battery/BatteryPage";
import TasksPage from "@/pages/tasks/TasksPage";
import ReportsPage from "@/pages/reports/ReportsPage";
import SettingsPage from "@/pages/settings/SettingsPage";

// AI and Integration Pages
const AIPage = lazy(() => import("@/pages/ai/AIPage"));
import IntegrationsPage from "@/pages/admin/IntegrationsPage";

// Manager Pages
import ManagerTeamPage from "@/pages/manager/ManagerTeamPage";
import ManagerTimeTrackingPage from "@/pages/manager/ManagerTimeTrackingPage";
import ManagerWorkBoardPage from "@/pages/manager/ManagerWorkBoardPage";
import ManagerLeavePage from "@/pages/manager/ManagerLeavePage";
import ManagerSitesPage from "@/pages/manager/ManagerSitesPage";
import ManagerMeetingsPage from "@/pages/manager/ManagerMeetingsPage";
import ManagerMemosPage from "@/pages/manager/ManagerMemosPage";

// Staff Pages
import StaffCurrentTasksPage from "@/pages/staff/StaffCurrentTasksPage";
import StaffMyTasksPage from "@/pages/staff/StaffMyTasksPage";
import StaffTelecomReportsPage from "@/pages/staff/StaffTelecomReportsPage";
import StaffBatteryReportsPage from "@/pages/staff/StaffBatteryReportsPage";
import StaffProfilePage from "@/pages/staff/StaffProfilePage";

// Admin Routes
import APIKeysPage from '@/pages/admin/APIKeysPage';
import DatabasePopulatePage from '@/pages/admin/DatabasePopulatePage';
import AdminSettings from '@/pages/admin/AdminSettings';
import CommunicationPage from '@/pages/admin/CommunicationPage';

// Account and Files Pages
import AccountPage from '@/pages/account/AccountPage';
import FilesPage from '@/pages/files/FilesPage';

// 404 Component
const NotFound = () => (
  <div className="min-h-screen flex items-center justify-center">
    <div className="text-center">
      <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-100">404</h1>
      <p className="text-gray-600 dark:text-gray-400 mt-2">Page not found</p>
      <button 
        onClick={() => window.history.back()} 
        className="mt-4 px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
      >
        Go Back
      </button>
    </div>
  </div>
);

function App() {
  return (
    <ErrorBoundary>
      <TooltipProvider>
            <BrowserRouter>
              <AuthProvider>
                <div className="min-h-screen bg-background">
                  <Suspense fallback={<Loader />}>
                    <Routes>
                      {/* Public Routes - Clock-in as entry point */}
                      <Route path="/" element={<ClockInPage />} />
                      <Route path="/clockin" element={<ClockInPage />} />
                      <Route path="/auth" element={<AuthPage />} />
                      <Route path="/login" element={<Navigate to="/auth" replace />} />
                      <Route path="/debug" element={<DebugPage />} />
                      
                      {/* Unified Dashboard Routes - Single layout handles all dashboard routes */}
                      <Route path="/dashboard/*" element={<UnifiedDashboardLayout />} />

                      {/* Legacy redirects */}
                      <Route path="/admin" element={<Navigate to="/dashboard/admin" replace />} />
                      <Route path="/manager" element={<Navigate to="/dashboard/manager" replace />} />
                      <Route path="/staff" element={<Navigate to="/dashboard/staff" replace />} />
                      <Route path="/accountant" element={<Navigate to="/dashboard/accountant" replace />} />
                      <Route path="/staff-admin" element={<Navigate to="/dashboard/staff-admin" replace />} />

                      {/* 404 fallback - Use NotFound component instead of redirect */}
                      <Route path="*" element={<NotFound />} />
                    </Routes>
                  </Suspense>
                </div>
                <Toaster />
              </AuthProvider>
            </BrowserRouter>
          </TooltipProvider>
    </ErrorBoundary>
  );
}

export default App;
