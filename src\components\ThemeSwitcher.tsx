
import { <PERSON>, <PERSON>, Monitor } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { useTheme } from "@/components/theme-provider";
import { 
  Tooltip, 
  TooltipContent, 
  TooltipProvider, 
  TooltipTrigger 
} from "@/components/ui/tooltip";
import { useState, useEffect } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

export const ThemeSwitcher = () => {
  const { theme, setTheme } = useTheme();
  const [showThemeDialog, setShowThemeDialog] = useState(false);

  useEffect(() => {
    const hasSeenThemeDialog = localStorage.getItem('hasSeenThemeDialog');
    if (!hasSeenThemeDialog) {
      setShowThemeDialog(true);
      localStorage.setItem('hasSeenThemeDialog', 'true');
    }
  }, []);

  return (
    <>
      <TooltipProvider>
        <Tooltip>
          <DropdownMenu>
            <TooltipTrigger asChild>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="outline"
                  size="icon"
                  className="w-10 h-10 rounded-full 
                    bg-white/10 dark:bg-gray-800/30
                    hover:bg-gray-100/80 dark:hover:bg-gray-700/50
                    border border-gray-200/30 dark:border-gray-700/30
                    transition-colors duration-200"
                >
                  {theme === 'dark' ? (
                    <Moon className="h-5 w-5 text-primary transition-all duration-200" />
                  ) : theme === 'light' ? (
                    <Sun className="h-5 w-5 text-primary transition-all duration-200" /> 
                  ) : (
                    <Monitor className="h-5 w-5 text-primary transition-all duration-200" />
                  )}
                  <span className="sr-only">Toggle theme</span>
                </Button>
              </DropdownMenuTrigger>
            </TooltipTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => setTheme("light")}>
                <Sun className="mr-2 h-4 w-4" />
                <span>Light</span>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setTheme("dark")}>
                <Moon className="mr-2 h-4 w-4" />
                <span>Dark</span>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => setTheme("system")}>
                <Monitor className="mr-2 h-4 w-4" />
                <span>System</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          <TooltipContent>
            <p>Change theme</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      <Dialog open={showThemeDialog} onOpenChange={setShowThemeDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Theme Preference</DialogTitle>
            <DialogDescription>
              Try our dark mode for a more comfortable viewing experience! 
              Click the theme switcher button in the top-right corner to toggle between light, dark, and system modes.
            </DialogDescription>
          </DialogHeader>
        </DialogContent>
      </Dialog>
    </>
  );
};
