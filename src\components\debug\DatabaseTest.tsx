import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { supabase } from '@/integrations/supabase/client';
import { Database, CheckCircle, XCircle, AlertCircle } from 'lucide-react';

interface TestResult {
  name: string;
  status: 'success' | 'error' | 'pending';
  message: string;
  details?: any;
}

export const DatabaseTest = () => {
  const [results, setResults] = useState<TestResult[]>([]);
  const [testing, setTesting] = useState(false);

  const addResult = (result: TestResult) => {
    setResults(prev => [...prev, result]);
  };

  const clearResults = () => {
    setResults([]);
  };

  const testDatabaseConnection = async () => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('id')
        .limit(1);
      
      if (error) throw error;
      
      addResult({
        name: 'Database Connection',
        status: 'success',
        message: 'Successfully connected to Supabase',
        details: { recordCount: data?.length || 0 }
      });
    } catch (error: any) {
      addResult({
        name: 'Database Connection',
        status: 'error',
        message: error.message,
        details: error
      });
    }
  };

  const testTables = async () => {
    const tables = [
      'profiles',
      'departments', 
      'tasks',
      'projects',
      'memos',
      'api_keys',
      'integrations',
      'role_dashboard_configs'
    ];

    for (const table of tables) {
      try {
        const { data, error } = await supabase
          .from(table)
          .select('*')
          .limit(1);
        
        if (error) throw error;
        
        addResult({
          name: `Table: ${table}`,
          status: 'success',
          message: `Table exists and accessible`,
          details: { recordCount: data?.length || 0 }
        });
      } catch (error: any) {
        addResult({
          name: `Table: ${table}`,
          status: 'error',
          message: error.message,
          details: error
        });
      }
    }
  };

  const testAuth = async () => {
    try {
      const { data: { user }, error } = await supabase.auth.getUser();
      
      addResult({
        name: 'Authentication',
        status: user ? 'success' : 'error',
        message: user ? `Authenticated as ${user.email}` : 'Not authenticated',
        details: user
      });
    } catch (error: any) {
      addResult({
        name: 'Authentication',
        status: 'error',
        message: error.message,
        details: error
      });
    }
  };

  const testEdgeFunctions = async () => {
    const functionTests = [
      {
        name: 'analyze-content',
        body: { healthCheck: true },
        description: 'Content analysis with health check'
      },
      {
        name: 'ai-assistant',
        body: {
          message: 'Hello, this is a system test',
          userId: 'test-user-id',
          context: { role: 'admin', department: 'Engineering' }
        },
        description: 'AI assistant functionality'
      },
      {
        name: 'ai-agent-intent',
        body: {
          message: 'Test intent analysis',
          userId: 'test-user-id'
        },
        description: 'AI intent analysis'
      },
      {
        name: 'ai-agent-executor',
        body: {
          intent: 'test',
          parameters: {},
          userId: 'test-user-id'
        },
        description: 'AI task execution'
      },
      {
        name: 'ai-agent-response',
        body: {
          result: 'test result',
          userId: 'test-user-id'
        },
        description: 'AI response generation'
      },
      {
        name: 'ai-file-analyzer',
        body: {
          fileUrl: 'test-file-url',
          fileName: 'test.txt',
          userId: 'test-user-id'
        },
        description: 'File analysis capabilities'
      },
      {
        name: 'analyze-document',
        body: {
          documentUrl: 'test-document-url',
          documentType: 'pdf'
        },
        description: 'Document analysis'
      }
    ];

    for (const test of functionTests) {
      try {
        const startTime = Date.now();
        const { data, error } = await supabase.functions.invoke(test.name, {
          body: test.body
        });
        const responseTime = Date.now() - startTime;

        if (error) {
          // Check if it's a configuration error vs function not found
          if (error.message?.includes('not found') || error.message?.includes('404')) {
            addResult({
              name: `Edge Function: ${test.name}`,
              status: 'warning',
              message: 'Function not deployed or not found',
              details: error
            });
          } else {
            addResult({
              name: `Edge Function: ${test.name}`,
              status: 'error',
              message: error.message,
              details: error
            });
          }
        } else {
          addResult({
            name: `Edge Function: ${test.name}`,
            status: 'success',
            message: `Function responded in ${responseTime}ms`,
            details: { responseTime, data }
          });
        }
      } catch (error: any) {
        addResult({
          name: `Edge Function: ${test.name}`,
          status: 'error',
          message: error.message,
          details: error
        });
      }
    }
  };

  const runAllTests = async () => {
    setTesting(true);
    clearResults();
    
    try {
      await testDatabaseConnection();
      await testAuth();
      await testTables();
      await testEdgeFunctions();
    } catch (error) {
      console.error('Test suite failed:', error);
    } finally {
      setTesting(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'success':
        return <Badge variant="default" className="bg-green-500">PASS</Badge>;
      case 'error':
        return <Badge variant="destructive">FAIL</Badge>;
      default:
        return <Badge variant="secondary">PENDING</Badge>;
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5" />
          Database & System Test
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex flex-wrap gap-2">
            <Button
              onClick={runAllTests}
              disabled={testing}
              className="flex items-center gap-2"
            >
              {testing ? 'Testing...' : 'Run All Tests'}
            </Button>
            <Button
              variant="outline"
              onClick={testDatabaseConnection}
              disabled={testing}
              size="sm"
            >
              Test DB
            </Button>
            <Button
              variant="outline"
              onClick={testAuth}
              disabled={testing}
              size="sm"
            >
              Test Auth
            </Button>
            <Button
              variant="outline"
              onClick={testTables}
              disabled={testing}
              size="sm"
            >
              Test Tables
            </Button>
            <Button
              variant="outline"
              onClick={testEdgeFunctions}
              disabled={testing}
              size="sm"
            >
              Test Functions
            </Button>
            <Button
              variant="outline"
              onClick={clearResults}
              disabled={testing}
            >
              Clear Results
            </Button>
          </div>

          {results.length > 0 && (
            <div className="space-y-2">
              <h3 className="font-semibold">Test Results:</h3>
              {results.map((result, index) => (
                <div 
                  key={index}
                  className="flex items-center justify-between p-3 border rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    {getStatusIcon(result.status)}
                    <div>
                      <div className="font-medium">{result.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {result.message}
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {getStatusBadge(result.status)}
                  </div>
                </div>
              ))}
            </div>
          )}

          {results.length > 0 && (
            <div className="mt-4 p-4 bg-muted rounded-lg">
              <h4 className="font-semibold mb-2">Summary:</h4>
              <div className="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-green-500">
                    {results.filter(r => r.status === 'success').length}
                  </div>
                  <div className="text-sm text-muted-foreground">Passed</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-red-500">
                    {results.filter(r => r.status === 'error').length}
                  </div>
                  <div className="text-sm text-muted-foreground">Failed</div>
                </div>
                <div>
                  <div className="text-2xl font-bold">
                    {results.length}
                  </div>
                  <div className="text-sm text-muted-foreground">Total</div>
                </div>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
