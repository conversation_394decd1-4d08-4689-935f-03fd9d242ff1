import { useEffect, useState } from 'react';

export const InfiniteRefreshDebug = () => {
  const [renderCount, setRenderCount] = useState(0);
  const [lastRenderTime, setLastRenderTime] = useState(Date.now());

  useEffect(() => {
    setRenderCount(prev => prev + 1);
    setLastRenderTime(Date.now());
    
    // Log render information
    console.log(`🔄 Component rendered #${renderCount + 1} at ${new Date().toISOString()}`);
    
    // Check for rapid re-renders (more than 10 renders in 5 seconds)
    if (renderCount > 10) {
      const timeSinceFirstRender = Date.now() - lastRenderTime;
      if (timeSinceFirstRender < 5000) {
        console.warn('⚠️ Potential infinite refresh detected!', {
          renderCount,
          timeSinceFirstRender,
          rendersPerSecond: renderCount / (timeSinceFirstRender / 1000)
        });
      }
    }
  });

  return (
    <div className="fixed bottom-4 right-4 bg-red-500 text-white p-2 rounded text-xs z-50">
      Renders: {renderCount}
    </div>
  );
}; 