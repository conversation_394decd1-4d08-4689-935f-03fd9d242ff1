import { useState, useEffect, use<PERSON>allback } from "react";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { <PERSON>bs, <PERSON>bs<PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { 
  Upload, 
  FileText, 
  Search, 
  Download, 
  Eye, 
  Trash2,
  Filter,
  Plus,
  FolderPlus,
  Loader,
  AlertCircle,
  RefreshCw
} from "lucide-react";
import { FileUpload } from "@/components/shared/FileUpload";
import { DocumentList } from "./DocumentList";
import { DocumentViewer } from "./DocumentViewer";
import { FolderManagement } from "./FolderManagement";
import { api } from "@/lib/api";

interface DocumentType {
  id: string;
  title: string;
  file_name: string;
  file_type: string;
  file_size: number;
  category: string;
  tags: string[];
  folder_id?: string;
  uploaded_by: string;
  created_at: string;
  updated_at: string;
  file_path: string;
  access_level: string;
  uploader_name?: string;
}

interface Folder {
  id: string;
  name: string;
  parent_folder_id?: string;
  document_count?: number;
  access_level: string;
  created_at: string;
  updated_at: string;
}

export const DocumentManagement = () => {
  const [documents, setDocuments] = useState<DocumentType[]>([]);
  const [folders, setFolders] = useState<Folder[]>([]);
  const [selectedDocument, setSelectedDocument] = useState<DocumentType | null>(null);
  const [currentFolder, setCurrentFolder] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("documents");
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);
  const { toast } = useToast();

  const loadDocuments = useCallback(async (retryAttempt = 0) => {
    try {
      setLoading(true);
      setError(null);
      
      const filters = {
        folder_id: currentFolder,
        category: selectedCategory !== "all" ? selectedCategory : undefined,
        search: searchQuery || undefined
      };
      
      const response = await api.documents.list(filters);
      
      if (response.success && response.data) {
        setDocuments(Array.isArray(response.data) ? response.data : []);
        setRetryCount(0);
      } else {
        throw new Error(response.error?.message || 'Failed to load documents');
      }
    } catch (error: any) {
      console.error('Error loading documents:', error);
      
      // Enhanced error handling with specific error types
      let errorMessage = "Failed to load documents";
      
      if (error.message?.includes('relation') || error.message?.includes('column')) {
        errorMessage = "Database schema error - documents table may not exist or have incorrect structure";
      } else if (error.message?.includes('connection')) {
        errorMessage = "Database connection error - please check your internet connection";
      } else if (error.message?.includes('timeout')) {
        errorMessage = "Request timeout - the server is taking too long to respond";
      }
      
      setError(errorMessage);
      
      // Retry logic for transient errors
      if (retryAttempt < 2 && (error.message?.includes('connection') || error.message?.includes('timeout'))) {
        console.log(`Retrying document load (attempt ${retryAttempt + 1})`);
        setTimeout(() => {
          loadDocuments(retryAttempt + 1);
        }, 1000 * (retryAttempt + 1));
        return;
      }
      
      // Show user-friendly error message
      if (retryAttempt === 0) {
        toast({
          title: "Loading Error",
          description: errorMessage,
          variant: "destructive",
        });
      }
      
      // Set empty array as fallback
      setDocuments([]);
    } finally {
      setLoading(false);
    }
  }, [currentFolder, selectedCategory, searchQuery, toast]);

  const loadFolders = useCallback(async () => {
    try {
      const response = await api.documents.getFolders();
      if (response.success && response.data) {
        setFolders(Array.isArray(response.data) ? response.data : []);
      } else {
        // Don't throw error for folders - just use empty array
        console.warn('Failed to load folders:', response.error?.message);
        setFolders([]);
      }
    } catch (error: any) {
      console.warn('Error loading folders:', error);
      // Set empty array as fallback for folders
      setFolders([]);
    }
  }, []);

  useEffect(() => {
    loadDocuments();
    loadFolders();
  }, [loadDocuments, loadFolders]);

  const handleSearch = () => {
    loadDocuments();
  };

  const handleRetry = () => {
    setRetryCount(prev => prev + 1);
    loadDocuments();
  };

  const handleUploadComplete = (uploadedFiles: any[]) => {
    toast({
      title: "Upload Complete",
      description: `${uploadedFiles.length} file(s) uploaded successfully`,
    });
    loadDocuments();
  };

  const handleDeleteDocument = async (documentId: string) => {
    try {
      const response = await api.documents.delete(documentId);
      if (response.success) {
        toast({
          title: "Success",
          description: "Document deleted successfully",
        });
        loadDocuments();
      } else {
        throw new Error(response.error?.message || 'Failed to delete document');
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to delete document",
        variant: "destructive",
      });
    }
  };

  const handleCreateFolder = async (name: string, parentId?: string) => {
    try {
      const response = await api.documents.createFolder({
        name,
        parent_folder_id: parentId,
        access_level: 'department'
      });
      
      if (response.success) {
        toast({
          title: "Success",
          description: "Folder created successfully",
        });
        loadFolders();
      } else {
        throw new Error(response.error?.message || 'Failed to create folder');
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to create folder",
        variant: "destructive",
      });
    }
  };

  // Error state component
  const ErrorState = () => (
    <Card>
      <CardContent className="flex flex-col items-center justify-center h-64 space-y-4">
        <AlertCircle className="h-12 w-12 text-destructive" />
        <div className="text-center space-y-2">
          <h3 className="text-lg font-semibold">Failed to Load Documents</h3>
          <p className="text-sm text-muted-foreground max-w-md">
            {error || "There was a problem loading your documents. Please try again."}
          </p>
        </div>
        <Button onClick={handleRetry} variant="outline" className="mt-4">
          <RefreshCw className="h-4 w-4 mr-2" />
          Retry
        </Button>
      </CardContent>
    </Card>
  );

  // Loading state component
  const LoadingState = () => (
    <Card>
      <CardContent className="flex flex-col items-center justify-center h-64 space-y-4">
        <Loader className="h-8 w-8 animate-spin text-primary" />
        <p className="text-sm text-muted-foreground">Loading documents...</p>
      </CardContent>
    </Card>
  );

  // Empty state component
  const EmptyState = () => (
    <Card>
      <CardContent className="flex flex-col items-center justify-center h-64 space-y-4">
        <FileText className="h-12 w-12 text-muted-foreground" />
        <div className="text-center space-y-2">
          <h3 className="text-lg font-semibold">No Documents Found</h3>
          <p className="text-sm text-muted-foreground">
            {searchQuery || selectedCategory !== "all" 
              ? "No documents match your current filters. Try adjusting your search criteria."
              : "Upload your first document to get started."
            }
          </p>
        </div>
        {(!searchQuery && selectedCategory === "all") && (
          <Button onClick={() => setActiveTab("upload")} className="mt-4">
            <Upload className="h-4 w-4 mr-2" />
            Upload Document
          </Button>
        )}
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Document Management</h2>
          <p className="text-muted-foreground">Organize and manage your documents</p>
        </div>
        <div className="flex items-center gap-2">
          <Button onClick={() => setActiveTab("upload")} size="sm">
            <Upload className="h-4 w-4 mr-2" />
            Upload
          </Button>
          <Button onClick={() => setActiveTab("folders")} variant="outline" size="sm">
            <FolderPlus className="h-4 w-4 mr-2" />
            New Folder
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList>
          <TabsTrigger value="documents">Documents</TabsTrigger>
          <TabsTrigger value="upload">Upload</TabsTrigger>
          <TabsTrigger value="folders">Folders</TabsTrigger>
          <TabsTrigger value="viewer">Viewer</TabsTrigger>
        </TabsList>

        <TabsContent value="documents" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Document Library
                {documents.length > 0 && (
                  <Badge variant="secondary" className="ml-2">
                    {documents.length} documents
                  </Badge>
                )}
              </CardTitle>
              
              {/* Enhanced Search and Filter Controls */}
              <div className="flex flex-col sm:flex-row gap-4 mt-4">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search documents..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                    onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                  />
                </div>
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger className="w-full sm:w-48">
                    <SelectValue placeholder="Category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    <SelectItem value="reports">Reports</SelectItem>
                    <SelectItem value="contracts">Contracts</SelectItem>
                    <SelectItem value="presentations">Presentations</SelectItem>
                    <SelectItem value="images">Images</SelectItem>
                    <SelectItem value="other">Other</SelectItem>
                  </SelectContent>
                </Select>
                <Button onClick={handleSearch} variant="outline">
                  <Search className="h-4 w-4 mr-2" />
                  Search
                </Button>
              </div>
            </CardHeader>
            
            <CardContent>
              {/* Breadcrumb Navigation */}
              {currentFolder && (
                <div className="flex items-center gap-2 text-sm text-muted-foreground mb-4">
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => setCurrentFolder(null)}
                  >
                    Root
                  </Button>
                  <span>/</span>
                  <span>{folders.find(f => f.id === currentFolder)?.name}</span>
                </div>
              )}

              {/* Document List with Enhanced States */}
              {error ? (
                <ErrorState />
              ) : loading ? (
                <LoadingState />
              ) : documents.length === 0 ? (
                <EmptyState />
              ) : (
                <DocumentList
                  documents={documents}
                  folders={folders.filter(f => f.parent_folder_id === currentFolder)}
                  onSelectDocument={(doc) => {
                    setSelectedDocument(doc);
                    setActiveTab("viewer");
                  }}
                  onSelectFolder={setCurrentFolder}
                  onDeleteDocument={handleDeleteDocument}
                />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="upload" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-5 w-5" />
                Upload Documents
              </CardTitle>
            </CardHeader>
            <CardContent>
              <FileUpload
                onUploadComplete={handleUploadComplete}
                maxFiles={10}
                acceptedTypes={['image/*', 'application/pdf', '.doc,.docx,.txt,.xlsx,.pptx']}
                maxSize={50}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="folders" className="space-y-4">
          <FolderManagement
            folders={folders}
            onCreateFolder={handleCreateFolder}
            onRefresh={loadFolders}
          />
        </TabsContent>

        <TabsContent value="viewer" className="space-y-4">
          {selectedDocument ? (
            <DocumentViewer
              document={selectedDocument}
              onClose={() => setSelectedDocument(null)}
            />
          ) : (
            <Card>
              <CardContent className="flex items-center justify-center h-64">
                <div className="text-center">
                  <FileText className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <p className="text-muted-foreground">
                    Select a document from the library to view it here
                  </p>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};