import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Clock, MapPin, Users, Download, Filter } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { DocumentGenerator } from "@/lib/documentGenerator";

interface TeamMember {
  id: string;
  name: string;
  role: string;
  department: string;
  avatar?: string;
  status: "Online" | "Away" | "Offline";
  lastClockIn?: string;
  location?: string;
  isLoggedIn: boolean;
}

export const TeamOverview = () => {
  const [departmentFilter, setDepartmentFilter] = useState<string>("all");
  const { toast } = useToast();

  // Fetch team members with their clock-in status
  const { data: teamMembers = [], isLoading, error, refetch } = useQuery({
    queryKey: ['team-members', departmentFilter],
    queryFn: async () => {
      let profilesQuery = supabase
        .from('profiles')
        .select(`
          id,
          full_name,
          role,
          department:department_id (
            id,
            name
          )
        `)
        .neq('role', 'admin'); // Exclude admin from team view

      if (departmentFilter !== 'all') {
        profilesQuery = profilesQuery.eq('department_id', departmentFilter);
      }

      const { data: profiles, error: profilesError } = await profilesQuery;
      if (profilesError) throw profilesError;

      // Get clock-in data for today
      const today = new Date().toISOString().split('T')[0];
      const { data: clockIns, error: clockError } = await supabase
        .from('clock_in_records')
        .select('user_id, clock_in_time, clock_out_time')
        .gte('clock_in_time', `${today}T00:00:00`)
        .lte('clock_in_time', `${today}T23:59:59`)
        .order('clock_in_time', { ascending: false });

      if (clockError) throw clockError;

      // Get online/active sessions (simplified - checking if user has clocked in today without clocking out)
      const activeUsers = clockIns?.filter(record => 
        record.clock_out_time === null
      ).map(record => record.user_id) || [];

      return profiles?.map(profile => {
        const latestClockIn = clockIns?.find(record => record.user_id === profile.id);
        const isLoggedIn = activeUsers.includes(profile.id);
        
        return {
          id: profile.id,
          name: profile.full_name || 'Unknown User',
          role: profile.role || 'Staff',
          department: profile.department?.name || 'Unassigned',
          avatar: `/placeholder.svg`,
          status: isLoggedIn ? "Online" : (latestClockIn ? "Away" : "Offline") as "Online" | "Away" | "Offline",
          lastClockIn: latestClockIn?.clock_in_time 
            ? new Date(latestClockIn.clock_in_time).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
            : 'No clock-in today',
          location: profile.department?.name || 'Unassigned',
          isLoggedIn,
        };
      }) || [];
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 30 * 1000, // Auto-refresh every 30 seconds
  });

  // Fetch departments for filter
  const { data: departments = [] } = useQuery({
    queryKey: ['departments-list'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('departments')
        .select('id, name')
        .order('name');
      
      if (error) throw error;
      return data || [];
    },
  });

  const exportTeamData = async () => {
    try {
      const exportData = DocumentGenerator.formatDataForExport(
        teamMembers,
        {
          name: 'Name',
          role: 'Role',
          department: 'Department',
          status: 'Status',
          lastClockIn: 'Last Clock-in',
          location: 'Location',
        }
      );

      await DocumentGenerator.exportToExcel(exportData, {
        filename: 'team-overview',
        title: 'Team Overview Report',
        subtitle: 'Employee Status and Activity Report',
        companyName: 'Your Company',
      });
      
      toast({
        title: "Export Successful",
        description: "Team data exported to Excel file",
      });
    } catch (error) {
      toast({
        title: "Export Failed",
        description: "Failed to export team data",
        variant: "destructive",
      });
    }
  };

  const stats = {
    total: teamMembers.length,
    online: teamMembers.filter(m => m.status === "Online").length,
    away: teamMembers.filter(m => m.status === "Away").length,
    offline: teamMembers.filter(m => m.status === "Offline").length,
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center p-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          <span className="ml-2">Loading team data...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="text-center p-8">
          <p className="text-red-500">Error loading team data</p>
          <Button onClick={() => refetch()} className="mt-2">Try Again</Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Team Overview</h2>
          <p className="text-muted-foreground">Monitor your team's activity and status</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={exportTeamData} variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export Excel
          </Button>
          <Button onClick={() => refetch()} variant="outline" size="sm">
            Refresh
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardContent className="flex items-center p-6">
            <Users className="h-8 w-8 text-blue-600" />
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Total Team</p>
              <p className="text-2xl font-bold">{stats.total}</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="flex items-center p-6">
            <div className="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center">
              <div className="h-4 w-4 rounded-full bg-white"></div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Online</p>
              <p className="text-2xl font-bold">{stats.online}</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="flex items-center p-6">
            <div className="h-8 w-8 rounded-full bg-yellow-500 flex items-center justify-center">
              <div className="h-4 w-4 rounded-full bg-white"></div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Away</p>
              <p className="text-2xl font-bold">{stats.away}</p>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="flex items-center p-6">
            <div className="h-8 w-8 rounded-full bg-gray-500 flex items-center justify-center">
              <div className="h-4 w-4 rounded-full bg-white"></div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-muted-foreground">Offline</p>
              <p className="text-2xl font-bold">{stats.offline}</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filter */}
      <div className="flex items-center gap-2">
        <Filter className="h-4 w-4" />
        <Select value={departmentFilter} onValueChange={setDepartmentFilter}>
          <SelectTrigger className="w-48">
            <SelectValue placeholder="Filter by department" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Departments</SelectItem>
            {departments.map((dept) => (
              <SelectItem key={dept.id} value={dept.id}>
                {dept.name}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Team Members Grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {teamMembers.map((member) => (
          <Card key={member.id} className="bg-black/10 border-none shadow-lg rounded-2xl hover:bg-black/20 transition-all">
            <CardHeader className="pb-2">
              <div className="flex items-center space-x-4">
                <Avatar className="h-12 w-12">
                  <AvatarImage src={member.avatar} alt={member.name} />
                  <AvatarFallback>{member.name.charAt(0)}</AvatarFallback>
                </Avatar>
                <div>
                  <CardTitle className="text-lg font-medium">{member.name}</CardTitle>
                  <p className="text-sm text-muted-foreground">{member.role}</p>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center text-sm">
                  <div className={`w-2 h-2 rounded-full mr-2 ${
                    member.status === "Online" 
                      ? "bg-green-500" 
                      : member.status === "Away"
                      ? "bg-yellow-500"
                      : "bg-gray-500"
                  }`} />
                  {member.status}
                </div>
                <div className="flex items-center text-sm text-muted-foreground">
                  <Clock className="h-4 w-4 mr-2" />
                  Last clock-in: {member.lastClockIn}
                </div>
                <div className="flex items-center text-sm text-muted-foreground">
                  <MapPin className="h-4 w-4 mr-2" />
                  {member.location}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {teamMembers.length === 0 && (
        <div className="text-center py-8 text-muted-foreground">
          <Users className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <p>No team members found</p>
          <p className="text-sm">Team members will appear here when they join your department</p>
        </div>
      )}
    </div>
  );
};