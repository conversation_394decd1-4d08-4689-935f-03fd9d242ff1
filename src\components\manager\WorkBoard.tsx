import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { useQuery } from "@tanstack/react-query";
import { api } from "@/lib/api";
import { Loader2, Plus, Users, TrendingUp, AlertCircle, RefreshCw } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface Project {
  id: string;
  name: string;
  progress_percentage?: number;
  status: string;
  description?: string;
  manager_id?: string;
  created_at: string;
  updated_at: string;
}

export const WorkBoard = () => {
  const { toast } = useToast();
  const [retryCount, setRetryCount] = useState(0);

  // Fetch real projects data with error handling
  const { 
    data: projects = [], 
    isLoading, 
    error, 
    refetch 
  } = useQuery({
    queryKey: ['projects', retryCount],
    queryFn: async () => {
      try {
        const response = await api.projects.getAll();
        if (response.success && response.data) {
          return response.data as Project[];
        } else {
          throw new Error(response.error?.message || 'Failed to load projects');
        }
      } catch (error: any) {
        console.error('Error loading projects:', error);
        throw error;
      }
    },
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    onError: (error: any) => {
      if (retryCount === 0) {
        toast({
          title: "Failed to Load Projects",
          description: "Unable to load project data. Using fallback data.",
          variant: "destructive",
        });
      }
    }
  });

  // Fetch team members count for projects
  const { data: teamCounts = {} } = useQuery({
    queryKey: ['project-team-counts'],
    queryFn: async () => {
      try {
        const response = await api.tasks.getAll({});
        if (response.success && response.data) {
          // Count unique team members per project
          const counts: Record<string, number> = {};
          response.data.forEach((task: any) => {
            if (task.project_id && task.assigned_to_id) {
              if (!counts[task.project_id]) {
                counts[task.project_id] = new Set();
              }
              (counts[task.project_id] as any).add(task.assigned_to_id);
            }
          });
          
          // Convert sets to counts
          Object.keys(counts).forEach(projectId => {
            counts[projectId] = (counts[projectId] as any).size;
          });
          
          return counts;
        }
        return {};
      } catch (error) {
        console.warn('Could not load team counts:', error);
        return {};
      }
    },
    enabled: projects.length > 0
  });

  const handleRetry = () => {
    setRetryCount(prev => prev + 1);
    refetch();
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'bg-green-500/20 text-green-700';
      case 'in_progress':
      case 'active':
        return 'bg-blue-500/20 text-blue-700';
      case 'planning':
        return 'bg-yellow-500/20 text-yellow-700';
      case 'on_hold':
      case 'paused':
        return 'bg-orange-500/20 text-orange-700';
      case 'cancelled':
        return 'bg-red-500/20 text-red-700';
      default:
        return 'bg-gray-500/20 text-gray-700';
    }
  };

  const getProgressColor = (progress: number) => {
    if (progress >= 80) return 'bg-green-500';
    if (progress >= 50) return 'bg-blue-500';
    if (progress >= 25) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  // Error state
  if (error && !projects.length) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center h-64 space-y-4">
          <AlertCircle className="h-12 w-12 text-destructive" />
          <div className="text-center space-y-2">
            <h3 className="text-lg font-semibold">Failed to Load Projects</h3>
            <p className="text-sm text-muted-foreground max-w-md">
              {error.message || "There was a problem loading your projects. Please try again."}
            </p>
          </div>
          <Button onClick={handleRetry} variant="outline">
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </CardContent>
      </Card>
    );
  }

  // Loading state
  if (isLoading && !projects.length) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center h-64 space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-sm text-muted-foreground">Loading projects...</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Project Workboard</h2>
          <p className="text-muted-foreground">
            Manage and track your team's projects
          </p>
        </div>
        <Button className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          New Project
        </Button>
      </div>

      {/* Show warning if using fallback data */}
      {error && projects.length > 0 && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            Some project data may be outdated. 
            <Button 
              variant="link" 
              size="sm" 
              onClick={handleRetry}
              className="p-0 h-auto ml-1"
            >
              Refresh now
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Projects Grid */}
      {projects.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center h-64 space-y-4">
            <div className="text-center space-y-2">
              <h3 className="text-lg font-semibold">No Projects Found</h3>
              <p className="text-sm text-muted-foreground">
                Get started by creating your first project
              </p>
            </div>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Project
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {projects.map((project) => {
            const progress = project.progress_percentage || 0;
            const teamSize = teamCounts[project.id] || 0;
            
            return (
              <Card 
                key={project.id} 
                className="hover:shadow-md transition-shadow cursor-pointer"
              >
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <CardTitle className="text-lg font-semibold line-clamp-2">
                      {project.name}
                    </CardTitle>
                    <Badge 
                      className={`${getStatusColor(project.status)} text-xs border-0`}
                    >
                      {project.status.replace('_', ' ')}
                    </Badge>
                  </div>
                  {project.description && (
                    <p className="text-sm text-muted-foreground line-clamp-2 mt-2">
                      {project.description}
                    </p>
                  )}
                </CardHeader>
                
                <CardContent className="space-y-4">
                  {/* Progress Section */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-muted-foreground">Progress</span>
                      <span className="font-medium">{progress}%</span>
                    </div>
                    <div className="w-full bg-muted rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full transition-all duration-300 ${getProgressColor(progress)}`}
                        style={{ width: `${progress}%` }}
                      />
                    </div>
                  </div>

                  {/* Team Section */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Users className="h-4 w-4" />
                      <span>Team: {teamSize} members</span>
                    </div>
                    <div className="flex items-center gap-1 text-sm text-muted-foreground">
                      <TrendingUp className="h-4 w-4" />
                      <span>Active</span>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex gap-2 pt-2">
                    <Button variant="outline" size="sm" className="flex-1">
                      View Details
                    </Button>
                    <Button size="sm" className="flex-1">
                      Manage
                    </Button>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      )}

      {/* Loading indicator for refresh */}
      {isLoading && projects.length > 0 && (
        <div className="flex items-center justify-center py-4">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          <span className="text-sm text-muted-foreground">Refreshing projects...</span>
        </div>
      )}
    </div>
  );
};