/**
 * Optimized Dashboard Card Component
 * Uses React.memo and performance optimizations for better rendering
 */

import React, { memo, useMemo, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  TrendingUp, 
  TrendingDown, 
  Minus,
  MoreHorizontal,
  RefreshCw
} from 'lucide-react';

interface DashboardCardProps {
  title: string;
  value: string | number;
  change?: number;
  changeType?: 'increase' | 'decrease' | 'neutral';
  icon?: React.ReactNode;
  description?: string;
  loading?: boolean;
  error?: string;
  onRefresh?: () => void;
  className?: string;
  variant?: 'default' | 'compact' | 'detailed';
}

// Memoized trend icon component
const TrendIcon = memo<{ change: number; changeType?: string }>(({ change, changeType }) => {
  const iconProps = { className: "h-4 w-4" };
  
  if (changeType === 'increase' || (change > 0 && !changeType)) {
    return <TrendingUp {...iconProps} className="h-4 w-4 text-green-500" />;
  }
  
  if (changeType === 'decrease' || (change < 0 && !changeType)) {
    return <TrendingDown {...iconProps} className="h-4 w-4 text-red-500" />;
  }
  
  return <Minus {...iconProps} className="h-4 w-4 text-gray-500" />;
});

TrendIcon.displayName = 'TrendIcon';

// Memoized change badge component
const ChangeBadge = memo<{ change: number; changeType?: string }>(({ change, changeType }) => {
  const variant = useMemo(() => {
    if (changeType === 'increase' || (change > 0 && !changeType)) {
      return 'default';
    }
    if (changeType === 'decrease' || (change < 0 && !changeType)) {
      return 'destructive';
    }
    return 'secondary';
  }, [change, changeType]);

  const displayValue = useMemo(() => {
    const absChange = Math.abs(change);
    return `${change > 0 ? '+' : ''}${absChange}%`;
  }, [change]);

  return (
    <Badge variant={variant} className="text-xs">
      {displayValue}
    </Badge>
  );
});

ChangeBadge.displayName = 'ChangeBadge';

// Memoized loading skeleton
const LoadingSkeleton = memo(() => (
  <div className="animate-pulse">
    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
    <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
    <div className="h-3 bg-gray-200 rounded w-full"></div>
  </div>
));

LoadingSkeleton.displayName = 'LoadingSkeleton';

// Memoized error display
const ErrorDisplay = memo<{ error: string; onRefresh?: () => void }>(({ error, onRefresh }) => (
  <div className="text-center py-4">
    <p className="text-sm text-red-600 mb-2">{error}</p>
    {onRefresh && (
      <Button
        variant="outline"
        size="sm"
        onClick={onRefresh}
        className="text-xs"
      >
        <RefreshCw className="h-3 w-3 mr-1" />
        Retry
      </Button>
    )}
  </div>
));

ErrorDisplay.displayName = 'ErrorDisplay';

// Main optimized dashboard card component
export const OptimizedDashboardCard = memo<DashboardCardProps>(({
  title,
  value,
  change,
  changeType,
  icon,
  description,
  loading = false,
  error,
  onRefresh,
  className = '',
  variant = 'default'
}) => {
  // Memoize formatted value to prevent unnecessary recalculations
  const formattedValue = useMemo(() => {
    if (typeof value === 'number') {
      // Format large numbers with appropriate suffixes
      if (value >= 1000000) {
        return `${(value / 1000000).toFixed(1)}M`;
      }
      if (value >= 1000) {
        return `${(value / 1000).toFixed(1)}K`;
      }
      return value.toLocaleString();
    }
    return value;
  }, [value]);

  // Memoize card content based on variant
  const cardContent = useMemo(() => {
    if (loading) {
      return <LoadingSkeleton />;
    }

    if (error) {
      return <ErrorDisplay error={error} onRefresh={onRefresh} />;
    }

    switch (variant) {
      case 'compact':
        return (
          <div className="flex items-center justify-between">
            <div>
              <p className="text-2xl font-bold">{formattedValue}</p>
              <p className="text-xs text-muted-foreground">{title}</p>
            </div>
            {icon && <div className="text-muted-foreground">{icon}</div>}
          </div>
        );

      case 'detailed':
        return (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium">{title}</h3>
              {icon && <div className="text-muted-foreground">{icon}</div>}
            </div>
            <div className="space-y-1">
              <p className="text-3xl font-bold">{formattedValue}</p>
              {change !== undefined && (
                <div className="flex items-center space-x-2">
                  <TrendIcon change={change} changeType={changeType} />
                  <ChangeBadge change={change} changeType={changeType} />
                </div>
              )}
              {description && (
                <p className="text-xs text-muted-foreground">{description}</p>
              )}
            </div>
          </div>
        );

      default:
        return (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <h3 className="text-sm font-medium">{title}</h3>
              {icon && <div className="text-muted-foreground">{icon}</div>}
            </div>
            <div className="flex items-center space-x-2">
              <p className="text-2xl font-bold">{formattedValue}</p>
              {change !== undefined && (
                <>
                  <TrendIcon change={change} changeType={changeType} />
                  <ChangeBadge change={change} changeType={changeType} />
                </>
              )}
            </div>
            {description && (
              <p className="text-xs text-muted-foreground">{description}</p>
            )}
          </div>
        );
    }
  }, [
    loading,
    error,
    onRefresh,
    variant,
    formattedValue,
    title,
    icon,
    change,
    changeType,
    description
  ]);

  // Memoize refresh handler to prevent unnecessary re-renders
  const handleRefresh = useCallback(() => {
    if (onRefresh) {
      onRefresh();
    }
  }, [onRefresh]);

  // Memoize card header content
  const cardHeader = useMemo(() => {
    if (variant === 'compact') {
      return null;
    }

    return (
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {onRefresh && (
          <Button
            variant="ghost"
            size="sm"
            onClick={handleRefresh}
            className="h-8 w-8 p-0"
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
          </Button>
        )}
      </CardHeader>
    );
  }, [variant, title, onRefresh, handleRefresh, loading]);

  return (
    <Card className={`transition-all duration-200 hover:shadow-md ${className}`}>
      {cardHeader}
      <CardContent className={variant === 'compact' ? 'pt-6' : ''}>
        {cardContent}
      </CardContent>
    </Card>
  );
});

OptimizedDashboardCard.displayName = 'OptimizedDashboardCard';

// Export additional optimized components
export const OptimizedCardGrid = memo<{
  children: React.ReactNode;
  columns?: number;
  gap?: string;
  className?: string;
}>(({ children, columns = 4, gap = 'gap-4', className = '' }) => {
  const gridClass = useMemo(() => {
    const colClass = {
      1: 'grid-cols-1',
      2: 'grid-cols-1 md:grid-cols-2',
      3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
      4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
      5: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5',
      6: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6'
    }[columns] || 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4';

    return `grid ${colClass} ${gap} ${className}`;
  }, [columns, gap, className]);

  return (
    <div className={gridClass}>
      {children}
    </div>
  );
});

OptimizedCardGrid.displayName = 'OptimizedCardGrid';

// Performance monitoring hook for dashboard cards
export const useDashboardCardPerformance = (cardId: string) => {
  const startTime = useMemo(() => performance.now(), []);

  const measureRenderTime = useCallback(() => {
    const endTime = performance.now();
    const renderTime = endTime - startTime;
    
    // Log performance metrics in development
    if (process.env.NODE_ENV === 'development') {
      console.log(`Dashboard card ${cardId} render time: ${renderTime.toFixed(2)}ms`);
    }

    // Send to performance monitoring service in production
    if (process.env.NODE_ENV === 'production' && renderTime > 100) {
      // Performance monitoring integration would go here
      console.warn(`Slow dashboard card render: ${cardId} took ${renderTime.toFixed(2)}ms`);
    }
  }, [cardId, startTime]);

  React.useEffect(() => {
    measureRenderTime();
  });

  return { measureRenderTime };
};
