import { useState, useEffect } from "react";
import { useAuth } from "@/components/auth/AuthProvider";
import { supabase } from "@/integrations/supabase/client";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Building,
  Briefcase, 
  Calendar, 
  Edit3, 
  Save, 
  X,
  Upload,
  Camera,
  Check,
  AlertCircle,
  Loader2
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";

interface UserProfile {
  id: string;
  email: string;
  full_name: string;
  phone?: string;
  position?: string;
  department?: string;
  location?: string;
  bio?: string;
  avatar_url?: string;
  role: string;
  account_type: string;
  status: string;
  created_at: string;
  updated_at: string;
}

interface FormErrors {
  full_name?: string;
  phone?: string;
  position?: string;
  location?: string;
  bio?: string;
}

export function UserProfileModule() {
  const { userProfile, updateProfile } = useAuth();
  const { toast } = useToast();
  const [isEditing, setIsEditing] = useState(false);
  const [loading, setLoading] = useState(false);
  const [profileData, setProfileData] = useState<UserProfile | null>(null);
  const [departments, setDepartments] = useState<string[]>([]);
  const [errors, setErrors] = useState<FormErrors>({});
  const [hasChanges, setHasChanges] = useState(false);
  
  const [formData, setFormData] = useState({
    full_name: '',
    phone: '',
    position: '',
    department: '',
    location: '',
    bio: ''
  });

  const [originalFormData, setOriginalFormData] = useState({
    full_name: '',
    phone: '',
    position: '',
    department: '',
    location: '',
    bio: ''
  });

  useEffect(() => {
    if (userProfile) {
      setProfileData(userProfile);
      const initialData = {
        full_name: userProfile.full_name || '',
        phone: userProfile.phone || '',
        position: userProfile.position || '',
        department: userProfile.department || '',
        location: userProfile.location || '',
        bio: userProfile.bio || ''
      };
      setFormData(initialData);
      setOriginalFormData(initialData);
      loadDepartments();
    }
  }, [userProfile]);

  useEffect(() => {
    // Check if there are changes
    const hasFormChanges = Object.keys(formData).some(
      key => formData[key as keyof typeof formData] !== originalFormData[key as keyof typeof originalFormData]
    );
    setHasChanges(hasFormChanges);
  }, [formData, originalFormData]);

  const loadDepartments = async () => {
    try {
      const { data, error } = await supabase
        .from('departments')
        .select('name')
        .order('name');

      if (error) throw error;
      if (data) {
        setDepartments(data.map(dept => dept.name));
      }
    } catch (error) {
      console.log('Could not load departments:', error);
      // Provide default departments as fallback
      setDepartments([
        'Engineering',
        'Sales',
        'Marketing',
        'Operations',
        'Finance',
        'Human Resources',
        'IT Support',
        'Customer Service'
      ]);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    // Full name validation
    if (!formData.full_name.trim()) {
      newErrors.full_name = 'Full name is required';
    } else if (formData.full_name.trim().length < 2) {
      newErrors.full_name = 'Full name must be at least 2 characters';
    }

    // Phone validation (optional but must be valid if provided)
    if (formData.phone && !/^[\+]?[1-9][\d]{0,15}$/.test(formData.phone.replace(/[\s\-\(\)]/g, ''))) {
      newErrors.phone = 'Please enter a valid phone number';
    }

    // Position validation (optional but must be reasonable length)
    if (formData.position && formData.position.length > 100) {
      newErrors.position = 'Position must be less than 100 characters';
    }

    // Location validation (optional but must be reasonable length)
    if (formData.location && formData.location.length > 200) {
      newErrors.location = 'Location must be less than 200 characters';
    }

    // Bio validation (optional but must be reasonable length)
    if (formData.bio && formData.bio.length > 500) {
      newErrors.bio = 'Bio must be less than 500 characters';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (field: keyof typeof formData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error for this field when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const handleSave = async () => {
    if (!profileData) return;
    
    if (!validateForm()) {
      toast({
        title: "Validation Error",
        description: "Please fix the errors before saving",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {
      const { error } = await supabase
        .from('profiles')
        .update(formData)
        .eq('id', profileData.id);

      if (error) throw error;

      await updateProfile(formData);
      setOriginalFormData(formData);
      setIsEditing(false);
      setHasChanges(false);
      
      toast({
        title: "Profile Updated",
        description: "Your profile has been updated successfully!",
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setFormData(originalFormData);
    setErrors({});
    setIsEditing(false);
    setHasChanges(false);
  };

  const getFieldError = (field: keyof FormErrors) => {
    return errors[field];
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role.toLowerCase()) {
      case 'admin': return 'destructive';
      case 'manager': return 'default';
      case 'staff': return 'secondary';
      case 'accountant': return 'outline';
      case 'staff-admin': return 'default';
      default: return 'outline';
    }
  };

  if (!profileData) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading profile...</span>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            User Profile
          </CardTitle>
          <div className="flex items-center gap-2">
            {isEditing ? (
              <>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCancel}
                  disabled={loading}
                >
                  <X className="h-4 w-4 mr-1" />
                  Cancel
                </Button>
                <Button
                  size="sm"
                  onClick={handleSave}
                  disabled={loading || !hasChanges}
                >
                  {loading ? (
                    <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                  ) : (
                    <Save className="h-4 w-4 mr-1" />
                  )}
                  Save Changes
                </Button>
              </>
            ) : (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsEditing(true)}
              >
                <Edit3 className="h-4 w-4 mr-1" />
                Edit Profile
              </Button>
            )}
          </div>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Profile Header */}
          <div className="flex flex-col md:flex-row items-center md:items-start space-y-4 md:space-y-0 md:space-x-6">
            <div className="relative">
              <Avatar className="h-24 w-24 md:h-32 md:w-32">
                <AvatarImage src={profileData.avatar_url} />
                <AvatarFallback className="text-2xl">
                  {profileData.full_name?.charAt(0) || 'U'}
                </AvatarFallback>
              </Avatar>
              {isEditing && (
                <Button
                  size="sm"
                  variant="secondary"
                  className="absolute -bottom-2 -right-2 rounded-full p-2"
                  onClick={() => toast({ title: "Coming Soon", description: "Avatar upload will be available soon" })}
                >
                  <Camera className="h-4 w-4" />
                </Button>
              )}
            </div>
            
            <div className="flex-1 text-center md:text-left">
              <h3 className="text-2xl font-bold">
                {profileData.full_name || 'No name set'}
              </h3>
              <p className="text-muted-foreground mt-1">
                {profileData.position || profileData.role}
              </p>
              
              <div className="flex flex-wrap justify-center md:justify-start gap-2 mt-3">
                <Badge variant={getRoleBadgeColor(profileData.role)}>
                  {profileData.role}
                </Badge>
                {profileData.department && (
                  <Badge variant="outline">
                    <Building className="h-3 w-3 mr-1" />
                    {profileData.department}
                  </Badge>
                )}
                <Badge variant="outline" className="text-green-600">
                  <Check className="h-3 w-3 mr-1" />
                  Active
                </Badge>
              </div>
              
              <div className="mt-4 text-sm text-muted-foreground">
                <p>Member since {new Date(profileData.created_at).toLocaleDateString()}</p>
              </div>
            </div>
          </div>

          <Separator />

          {/* Contact Information */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Contact Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="full_name" className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  Full Name *
                </Label>
                {isEditing ? (
                  <div>
                    <Input
                      id="full_name"
                      value={formData.full_name}
                      onChange={(e) => handleInputChange('full_name', e.target.value)}
                      placeholder="Enter your full name"
                      className={getFieldError('full_name') ? 'border-destructive' : ''}
                    />
                    {getFieldError('full_name') && (
                      <p className="text-sm text-destructive mt-1 flex items-center gap-1">
                        <AlertCircle className="h-3 w-3" />
                        {getFieldError('full_name')}
                      </p>
                    )}
                  </div>
                ) : (
                  <div className="flex items-center gap-2 p-3 border rounded-md bg-muted/30">
                    <span>{profileData.full_name || 'Not set'}</span>
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="email" className="flex items-center gap-2">
                  <Mail className="h-4 w-4" />
                  Email Address
                </Label>
                <div className="flex items-center gap-2 p-3 border rounded-md bg-muted">
                  <span className="text-muted-foreground">{profileData.email}</span>
                  <Badge variant="outline" className="text-xs">Verified</Badge>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone" className="flex items-center gap-2">
                  <Phone className="h-4 w-4" />
                  Phone Number
                </Label>
                {isEditing ? (
                  <div>
                    <Input
                      id="phone"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      placeholder="Enter your phone number"
                      className={getFieldError('phone') ? 'border-destructive' : ''}
                    />
                    {getFieldError('phone') && (
                      <p className="text-sm text-destructive mt-1 flex items-center gap-1">
                        <AlertCircle className="h-3 w-3" />
                        {getFieldError('phone')}
                      </p>
                    )}
                  </div>
                ) : (
                  <div className="flex items-center gap-2 p-3 border rounded-md bg-muted/30">
                    <span>{profileData.phone || 'Not set'}</span>
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="location" className="flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  Location
                </Label>
                {isEditing ? (
                  <div>
                    <Input
                      id="location"
                      value={formData.location}
                      onChange={(e) => handleInputChange('location', e.target.value)}
                      placeholder="Enter your location"
                      className={getFieldError('location') ? 'border-destructive' : ''}
                    />
                    {getFieldError('location') && (
                      <p className="text-sm text-destructive mt-1 flex items-center gap-1">
                        <AlertCircle className="h-3 w-3" />
                        {getFieldError('location')}
                      </p>
                    )}
                  </div>
                ) : (
                  <div className="flex items-center gap-2 p-3 border rounded-md bg-muted/30">
                    <span>{profileData.location || 'Not set'}</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          <Separator />

          {/* Professional Information */}
          <div>
            <h4 className="text-lg font-semibold mb-4">Professional Information</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="position" className="flex items-center gap-2">
                  <Briefcase className="h-4 w-4" />
                  Position
                </Label>
                {isEditing ? (
                  <div>
                    <Input
                      id="position"
                      value={formData.position}
                      onChange={(e) => handleInputChange('position', e.target.value)}
                      placeholder="Enter your position"
                      className={getFieldError('position') ? 'border-destructive' : ''}
                    />
                    {getFieldError('position') && (
                      <p className="text-sm text-destructive mt-1 flex items-center gap-1">
                        <AlertCircle className="h-3 w-3" />
                        {getFieldError('position')}
                      </p>
                    )}
                  </div>
                ) : (
                  <div className="flex items-center gap-2 p-3 border rounded-md bg-muted/30">
                    <span>{profileData.position || 'Not set'}</span>
                  </div>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="department" className="flex items-center gap-2">
                  <Building className="h-4 w-4" />
                  Department
                </Label>
                {isEditing ? (
                  <Select
                    value={formData.department}
                    onValueChange={(value) => handleInputChange('department', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select department" />
                    </SelectTrigger>
                    <SelectContent>
                      {departments.map((dept) => (
                        <SelectItem key={dept} value={dept}>
                          {dept}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                ) : (
                  <div className="flex items-center gap-2 p-3 border rounded-md bg-muted/30">
                    <span>{profileData.department || 'Not set'}</span>
                  </div>
                )}
              </div>
            </div>
          </div>

          <Separator />

          {/* Bio Section */}
          <div>
            <h4 className="text-lg font-semibold mb-4">About</h4>
            <div className="space-y-2">
              <Label htmlFor="bio">Bio</Label>
              {isEditing ? (
                <div>
                  <Textarea
                    id="bio"
                    value={formData.bio}
                    onChange={(e) => handleInputChange('bio', e.target.value)}
                    placeholder="Tell us about yourself..."
                    rows={4}
                    className={getFieldError('bio') ? 'border-destructive' : ''}
                  />
                  <div className="flex justify-between items-center mt-1">
                    {getFieldError('bio') ? (
                      <p className="text-sm text-destructive flex items-center gap-1">
                        <AlertCircle className="h-3 w-3" />
                        {getFieldError('bio')}
                      </p>
                    ) : (
                      <div />
                    )}
                    <p className="text-sm text-muted-foreground">
                      {formData.bio.length}/500 characters
                    </p>
                  </div>
                </div>
              ) : (
                <div className="p-3 border rounded-md bg-muted/30 min-h-[100px]">
                  <p className="whitespace-pre-wrap">
                    {profileData.bio || 'No bio provided yet. Click edit to add information about yourself.'}
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Save indicator for edited form */}
          {isEditing && hasChanges && (
            <div className="flex items-center gap-2 p-3 bg-blue-50 border border-blue-200 rounded-md">
              <AlertCircle className="h-4 w-4 text-blue-600" />
              <span className="text-sm text-blue-800">
                You have unsaved changes. Click "Save Changes" to keep your updates.
              </span>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
