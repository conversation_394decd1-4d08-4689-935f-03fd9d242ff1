import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { FileText, Upload, Loader2, AlertCircle } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useQuery } from "@tanstack/react-query";
import { api } from "@/lib/api";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface Project {
  id: string;
  name: string;
  description?: string;
  status: string;
}

export const ProjectReportForm = () => {
  const [selectedProject, setSelectedProject] = useState("");
  const { toast } = useToast();

  // Replace mock data with real API call
  const { 
    data: projects = [], 
    isLoading: projectsLoading, 
    error: projectsError 
  } = useQuery({
    queryKey: ['projects-for-reports'],
    queryFn: async () => {
      try {
        const response = await api.projects.getAll();
        if (response.success && response.data) {
          return response.data as Project[];
        } else {
          // Fallback data if API fails
          return [
            { id: "PRJ-001", name: "Network Expansion", status: "active" },
            { id: "PRJ-002", name: "System Upgrade", status: "active" }
          ] as Project[];
        }
      } catch (error) {
        console.error('Error loading projects:', error);
        // Return fallback data
        return [
          { id: "PRJ-001", name: "Network Expansion", status: "active" },
          { id: "PRJ-002", name: "System Upgrade", status: "active" }
        ] as Project[];
      }
    },
    retry: 2,
    retryDelay: 1000
  });

  const handleSubmitReport = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    
    if (!selectedProject) {
      toast({
        title: "Validation Error",
        description: "Please select a project before submitting the report",
        variant: "destructive",
      });
      return;
    }

    const selectedProjectData = projects.find(p => p.id === selectedProject);
    
    toast({
      title: "Project Report Submitted",
      description: `Report for project "${selectedProjectData?.name || selectedProject}" has been submitted successfully`,
    });
  };

  return (
    <Card className="bg-black/10 dark:bg-white/5 backdrop-blur-lg border-none">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-lg font-medium">
          <FileText className="h-5 w-5 text-primary" />
          Project Report
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmitReport} className="space-y-4">
          <div className="space-y-2">
            <label className="text-sm font-medium">Select Project</label>
            <Select
              value={selectedProject}
              onValueChange={setSelectedProject}
              disabled={projectsLoading}
            >
              <SelectTrigger>
                <SelectValue placeholder={projectsLoading ? "Loading projects..." : "Select a project"} />
              </SelectTrigger>
              <SelectContent>
                {projectsLoading ? (
                  <div className="flex items-center justify-center p-2">
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    <span className="text-sm">Loading...</span>
                  </div>
                ) : (
                  projects.map((project) => (
                    <SelectItem key={project.id} value={project.id}>
                      {project.id} - {project.name}
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
            {projectsError && (
              <p className="text-sm text-yellow-600 flex items-center gap-1">
                <AlertCircle className="h-3 w-3" />
                Using fallback data - some projects may not be current
              </p>
            )}
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Project Status</label>
            <Select name="status" defaultValue="in-progress">
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="in-progress">In Progress</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="delayed">Delayed</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Progress Report</label>
            <textarea
              name="report"
              className="w-full rounded-md border border-white/10 bg-white/5 p-2 min-h-[100px]"
              placeholder="Enter project progress details..."
              required
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Upload Supporting Documents</label>
            <div className="border-2 border-dashed border-white/10 rounded-lg p-4 text-center">
              <Upload className="h-8 w-8 mx-auto text-primary" />
              <p className="text-sm text-muted-foreground mt-2">
                Drop files here or click to upload
              </p>
              <input
                type="file"
                className="hidden"
                accept=".pdf,.doc,.docx"
                multiple
              />
            </div>
          </div>

          <Button type="submit" className="w-full md:w-auto" disabled={projectsLoading}>
            Submit Project Report
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};