import { useQuery, UseQueryResult } from "@tanstack/react-query";
import { useAuth } from "@/components/auth/AuthProvider";
import { api } from "@/lib/api";
import { QUERY_CACHE_CONFIG, QUERY_KEYS, PAGI<PERSON>TION_CONFIG } from "@/lib/performance-config";

export interface DashboardStats {
  totalUsers: number;
  activeProjects: number;
  completedTasks: number;
  totalRevenue: number;
  pendingApprovals: number;
  systemHealth: number;
  onlineUsers: number;
  criticalAlerts: number;
}

export interface ChartDataPoint {
  name: string;
  value: number;
  change?: number;
}

export interface ActivityItem {
  id: string;
  action: string;
  user: string;
  timestamp: string;
  description: string;
  type: 'info' | 'warning' | 'success' | 'error';
}

export interface DashboardData {
  stats: DashboardStats;
  chartData: ChartDataPoint[];
  pieChartData: ChartDataPoint[];
  recentActivity: ActivityItem[];
  performanceMetrics: {
    tasksThisWeek: number;
    hoursWorked: number;
    projectsCompleted: number;
    efficiency: number;
  };
  financialSummary: {
    totalIncome: number;
    totalExpenses: number;
    netProfit: number;
    budgetUtilization: number;
  };
}

export const useDashboardData = (): UseQueryResult<DashboardData> & {
  refetch: () => void;
} => {
  const { userProfile } = useAuth();

  const query = useQuery({
    queryKey: QUERY_KEYS.dashboard.data(userProfile?.role, userProfile?.id),
    queryFn: async (): Promise<DashboardData> => {
      try {
        console.log('🔄 Fetching dashboard data for:', userProfile?.role);

        // Fetch data using the enhanced API
        const [
          dashboardStatsResult,
          projectsResult,
          tasksResult,
          activitiesResult,
          financialResult
        ] = await Promise.all([
          api.system.getDashboardStats(),
          api.projects.getAll(),
          api.tasks.getAll({ 
            assignedTo: userProfile?.role === 'staff' ? userProfile.id : undefined 
          }),
          api.system.getActivityLogs(PAGINATION_CONFIG.dashboardLimit),
          userProfile?.role === 'admin' || userProfile?.role === 'accountant' || userProfile?.role === 'manager' 
            ? api.financial.invoices.getAll() 
            : Promise.resolve({ data: [], success: true, error: null })
        ]);

        // Process results with proper error handling
        const dashboardStats = dashboardStatsResult.success ? dashboardStatsResult.data : null;
        const projects = projectsResult.success ? projectsResult.data || [] : [];
        const tasks = tasksResult.success ? tasksResult.data || [] : [];
        const activities = activitiesResult.success ? activitiesResult.data || [] : [];
        const invoices = financialResult.success ? 
          Array.isArray(financialResult.data) ? financialResult.data : [] : [];

        console.log('📊 Dashboard data fetched:', {
          projects: Array.isArray(projects) ? projects.length : 0,
          tasks: Array.isArray(tasks) ? tasks.length : 0,
          activities: Array.isArray(activities) ? activities.length : 0,
          invoices: Array.isArray(invoices) ? invoices.length : 0
        });

        // Calculate stats from real data
        const stats: DashboardStats = {
          totalUsers: dashboardStats?.users && Array.isArray(dashboardStats.users) ? dashboardStats.users.length : 0,
          activeProjects: Array.isArray(projects) ? projects.filter((p: any) => p.status === 'active').length : 0,
          completedTasks: Array.isArray(tasks) ? tasks.filter((t: any) => t.status === 'completed').length : 0,
          totalRevenue: Array.isArray(invoices) ? invoices
            .filter((inv: any) => inv.payment_status === 'paid')
            .reduce((sum: number, inv: any) => sum + (inv.total_amount || 0), 0) : 0,
          pendingApprovals: Array.isArray(tasks) ? tasks.filter((t: any) => t.status === 'pending').length : 0,
          systemHealth: 98, // Static for now
          onlineUsers: dashboardStats?.users && Array.isArray(dashboardStats.users) ? 
            Math.min(dashboardStats.users.length, 15) : 0, // Mock active users
          criticalAlerts: Array.isArray(tasks) ? tasks.filter((t: any) => 
            t.priority === 'high' && t.status !== 'completed'
          ).length : 0
        };

        // Generate chart data from real projects
        const chartData: ChartDataPoint[] = Array.isArray(projects) ? 
          projects.slice(0, 5).map((project: any) => ({
            name: project.name || 'Unnamed Project',
            value: project.progress_percentage || Math.floor(Math.random() * 100),
            change: Math.random() * 20 - 10 // Mock change for now
          })) : [
            { name: 'Sample Project', value: 75 },
            { name: 'Demo Project', value: 45 }
          ];

        // Task status distribution for pie chart
        const taskStatusCounts = Array.isArray(tasks) ? 
          tasks.reduce((acc: Record<string, number>, task: any) => {
            const status = task.status || 'unknown';
            acc[status] = (acc[status] || 0) + 1;
            return acc;
          }, {}) : { 'pending': 2, 'in_progress': 3, 'completed': 1 };

        const pieChartData: ChartDataPoint[] = Object.entries(taskStatusCounts).map(([status, count]) => ({
          name: status.charAt(0).toUpperCase() + status.slice(1),
          value: count as number
        }));

        // Recent activity from real data
        const recentActivity: ActivityItem[] = Array.isArray(activities) ? 
          activities.map((activity: any) => ({
            id: activity.id,
            action: activity.action || 'System Activity',
            user: activity.user?.full_name || 'System',
            timestamp: activity.created_at,
            description: activity.description || activity.action || 'Activity performed',
            type: activity.action?.includes('error') ? 'error' : 
                  activity.action?.includes('warning') ? 'warning' :
                  activity.action?.includes('completed') ? 'success' : 'info'
          })) : [];

        // Performance metrics based on real data
        const userTasks = userProfile?.role === 'staff' && Array.isArray(tasks) ? tasks : [];
        const now = new Date();
        const weekAgo = new Date();
        weekAgo.setDate(weekAgo.getDate() - 7);
        
        const performanceMetrics = {
          tasksThisWeek: Array.isArray(userTasks) ? userTasks.filter((t: any) => {
            const taskDate = new Date(t.created_at);
            return taskDate >= weekAgo;
          }).length : 0,
          hoursWorked: userProfile?.role === 'staff' ? 
            Math.floor(Math.random() * 40) + 20 : // Mock hours for now
            0,
          projectsCompleted: userProfile?.role === 'staff' && Array.isArray(projects) && Array.isArray(tasks) ? 
            projects.filter((p: any) => p.status === 'completed' && 
              tasks.some((t: any) => t.project_id === p.id && t.assigned_to_id === userProfile.id)
            ).length : 
            Array.isArray(projects) ? projects.filter((p: any) => p.status === 'completed').length : 0,
          efficiency: Array.isArray(userTasks) && userTasks.length > 0 ? 
            Math.round((userTasks.filter((t: any) => t.status === 'completed').length / userTasks.length) * 100) : 
            85 // Default efficiency
        };

        // Financial summary from real data
        const paidInvoices = Array.isArray(invoices) ? invoices.filter((inv: any) => inv.payment_status === 'paid') : [];
        const pendingInvoices = Array.isArray(invoices) ? invoices.filter((inv: any) => inv.payment_status === 'pending') : [];
        
        const totalIncome = paidInvoices.reduce((sum: number, inv: any) => sum + (inv.total_amount || 0), 0);
        const totalExpenses = pendingInvoices.reduce((sum: number, inv: any) => sum + (inv.total_amount || 0), 0);

        const financialSummary = {
          totalIncome,
          totalExpenses,
          netProfit: totalIncome - totalExpenses,
          budgetUtilization: Array.isArray(projects) && projects.length > 0 ? 
            Math.round((projects.reduce((sum: number, p: any) => sum + (p.budget_spent || 0), 0) / 
            Math.max(projects.reduce((sum: number, p: any) => sum + (p.budget || 1), 0), 1)) * 100) : 0
        };

        console.log('✅ Dashboard data processed successfully');

        return {
          stats,
          chartData,
          pieChartData,
          recentActivity,
          performanceMetrics,
          financialSummary
        };
      } catch (error) {
        console.error('❌ Error fetching dashboard data:', error);
        
        // Return fallback data structure
        return {
          stats: {
            totalUsers: 0,
            activeProjects: 0,
            completedTasks: 0,
            totalRevenue: 0,
            pendingApprovals: 0,
            systemHealth: 0,
            onlineUsers: 0,
            criticalAlerts: 0
          },
          chartData: [
            { name: 'Sample Project', value: 75 },
            { name: 'Demo Project', value: 45 },
            { name: 'Test Project', value: 90 }
          ],
          pieChartData: [
            { name: 'Completed', value: 3 },
            { name: 'In Progress', value: 5 },
            { name: 'Pending', value: 2 }
          ],
          recentActivity: [],
          performanceMetrics: {
            tasksThisWeek: 0,
            hoursWorked: 0,
            projectsCompleted: 0,
            efficiency: 0
          },
          financialSummary: {
            totalIncome: 0,
            totalExpenses: 0,
            netProfit: 0,
            budgetUtilization: 0
          }
        };
      }
    },
    retry: (failureCount, error: any) => {
      // Don't retry on 4xx errors (client errors)
      if (error?.status >= 400 && error?.status < 500) {
        return false;
      }
      return failureCount < 2;
    },
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    ...QUERY_CACHE_CONFIG.dynamic, // Use dynamic cache config for dashboard data
    enabled: !!userProfile?.id,
    refetchOnReconnect: true
  });

  return {
    ...query,
    refetch: query.refetch
  };
};