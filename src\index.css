
@tailwind base;
@tailwind components;
@tailwind utilities;
 
@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 0 100% 51%;  /* #ff1c04 */
    --primary-foreground: 210 40% 98%;
    --secondary: 197 86% 43%;  /* #0FA0CE */
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 0 100% 51%;
    --radius: 60px;
  }
 
  .dark {
    --background: 15 23% 6%;
    --foreground: 210 40% 98%;
    --card: 15 23% 8%;
    --card-foreground: 210 40% 98%;
    --popover: 15 23% 8%;
    --popover-foreground: 210 40% 98%;
    --primary: 0 100% 51%;
    --primary-foreground: 210 40% 98%;
    --secondary: 197 86% 43%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 0 100% 51%;
  }

  /* Modern Glass Morphism Cards with 30px radius */
  .glass-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 30px;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
  }

  .glass-card:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 28, 4, 0.3);
  }

  .dark .glass-card {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.3);
  }

  .dark .glass-card:hover {
    background: rgba(0, 0, 0, 0.25);
  }

  .glassmorphism {
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 30px;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.05);
  }

  .dark .glassmorphism {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.05);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.2);
  }

  /* Modern Gradient Backgrounds */
  .modern-gradient {
    background: linear-gradient(135deg, 
      rgba(255, 28, 4, 0.1) 0%, 
      rgba(0, 0, 0, 0.1) 50%, 
      rgba(255, 28, 4, 0.05) 100%);
  }

  .dark-gradient {
    background: linear-gradient(135deg, 
      rgba(255, 28, 4, 0.2) 0%, 
      rgba(0, 0, 0, 0.15) 50%, 
      rgba(0, 0, 0, 0.8) 100%);
  }

  /* Enhanced Card Styles with 30px radius */
  .modern-card {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(229, 231, 235, 0.5);
    border-radius: 30px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.05);
    transition: all 0.5s ease;
    transform: translateY(0);
  }

  .modern-card:hover {
    box-shadow: 0 25px 50px -12px rgba(255, 28, 4, 0.1);
    transform: translateY(-4px);
  }

  .dark .modern-card {
    background: rgba(17, 24, 39, 0.8);
    border: 1px solid rgba(55, 65, 81, 0.5);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.2);
  }

  /* Stats Card Enhancements with 30px radius */
  .stats-card {
    position: relative;
    overflow: hidden;
    border-radius: 30px;
    padding: 1.5rem;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
  }

  .stats-card:hover {
    transform: scale(1.05);
  }

  .dark .stats-card {
    background: linear-gradient(135deg, rgba(17, 24, 39, 0.9), rgba(31, 41, 55, 0.7));
    border: 1px solid rgba(55, 65, 81, 0.3);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.3);
  }

  .stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 28, 4, 0.1), transparent, rgba(15, 160, 206, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .stats-card:hover::before {
    opacity: 1;
  }

  /* Modern Charts and Data Visualization with 30px radius */
  .chart-container {
    background: rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(20px);
    border-radius: 30px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
  }

  .dark .chart-container {
    background: rgba(17, 24, 39, 0.5);
    border: 1px solid rgba(55, 65, 81, 0.2);
  }

  /* Animated Elements */
  .pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
  }

  @keyframes pulse-glow {
    0%, 100% {
      box-shadow: 0 0 20px rgba(255, 28, 4, 0.3);
    }
    50% {
      box-shadow: 0 0 40px rgba(255, 28, 4, 0.6);
    }
  }

  .floating-animation {
    animation: floating 3s ease-in-out infinite;
  }

  @keyframes floating {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  /* Modern Typography */
  .modern-heading {
    color: transparent;
    background: linear-gradient(to right, #ff1c04, #ff1c04, #0FA0CE);
    background-clip: text;
    -webkit-background-clip: text;
    font-weight: bold;
    letter-spacing: -0.025em;
  }

  /* Sidebar Enhancements with 30px radius */
  .modern-sidebar {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.05);
  }

  .dark .modern-sidebar {
    background: rgba(17, 24, 39, 0.8);
    border-right: 1px solid rgba(55, 65, 81, 0.2);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.2);
  }

  /* Navigation Items with 30px radius */
  .nav-item {
    position: relative;
    overflow: hidden;
    border-radius: 30px;
    padding: 0.75rem;
    margin: 0.25rem;
    background: transparent;
    border: 1px solid transparent;
    transition: all 0.3s ease;
  }

  .nav-item:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 28, 4, 0.2);
  }

  .dark .nav-item:hover {
    background: rgba(255, 255, 255, 0.05);
  }

  .nav-item.active {
    background: linear-gradient(to right, rgba(255, 28, 4, 0.2), rgba(15, 160, 206, 0.2));
    border-color: rgba(255, 28, 4, 0.4);
    box-shadow: 0 10px 15px -3px rgba(255, 28, 4, 0.2);
  }

  .nav-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to right, rgba(255, 28, 4, 0.1), rgba(15, 160, 206, 0.1));
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 30px;
  }

  .nav-item:hover::before {
    opacity: 1;
  }

  /* Modern Buttons with 30px radius */
  .modern-btn {
    position: relative;
    overflow: hidden;
    border-radius: 30px;
    padding: 0.75rem 1.5rem;
    background: linear-gradient(to right, #ff1c04, #e01703);
    color: white;
    font-weight: 600;
    box-shadow: 0 20px 25px -5px rgba(255, 28, 4, 0.3);
    transition: all 0.3s ease;
    transform: scale(1);
  }

  .modern-btn:hover {
    box-shadow: 0 25px 50px -12px rgba(255, 28, 4, 0.5);
    transform: scale(1.05);
  }

  /* Data Tables with 30px radius */
  .modern-table {
    background: rgba(255, 255, 255, 0.5);
    backdrop-filter: blur(20px);
    border-radius: 30px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    overflow: hidden;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
  }

  .dark .modern-table {
    background: rgba(17, 24, 39, 0.5);
    border: 1px solid rgba(55, 65, 81, 0.2);
  }

  .modern-table th {
    background: linear-gradient(to right, rgba(255, 28, 4, 0.1), rgba(15, 160, 206, 0.1));
    color: rgb(17, 24, 39);
    font-weight: 600;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  }

  .dark .modern-table th {
    color: rgb(249, 250, 251);
    border-bottom: 1px solid rgba(55, 65, 81, 0.2);
  }

  .modern-table tr:hover {
    background: rgba(255, 255, 255, 0.2);
    transition: background-color 0.2s ease;
  }

  .dark .modern-table tr:hover {
    background: rgba(255, 255, 255, 0.05);
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }
  body {
    background: linear-gradient(135deg, rgb(249, 250, 251), white, rgb(243, 244, 246));
    color: hsl(var(--foreground));
    min-height: 100vh;
  }

  .dark body {
    background: linear-gradient(135deg, rgb(17, 24, 39), rgb(31, 41, 55), rgb(17, 24, 39));
  }
}

@layer components {
  .dashboard-container {
    min-height: 100vh;
    background: linear-gradient(135deg,
      rgba(249, 250, 251, 0.5),
      rgba(255, 255, 255, 0.3),
      rgba(239, 246, 255, 0.5)
    );
  }

  .dark .dashboard-container {
    background: linear-gradient(135deg,
      rgba(17, 24, 39, 0.8),
      rgba(31, 41, 55, 0.6),
      rgba(17, 24, 39, 0.8)
    );
  }

  .content-wrapper {
    backdrop-filter: blur(4px);
    background: rgba(255, 255, 255, 0.3);
    min-height: 100vh;
    transition: all 0.3s ease;
  }

  .dark .content-wrapper {
    background: rgba(0, 0, 0, 0.2);
  }

  /* Futuristic AI Interface Styles */
  .neural-glow {
    box-shadow: 
      0 0 20px rgba(56, 189, 248, 0.3),
      0 0 40px rgba(147, 51, 234, 0.2),
      inset 0 0 20px rgba(56, 189, 248, 0.1);
  }

  .cyber-border {
    background: linear-gradient(45deg, transparent 30%, rgba(56, 189, 248, 0.2) 50%, transparent 70%);
    background-size: 20px 20px;
    animation: cyber-scan 3s linear infinite;
  }

  .quantum-pulse {
    animation: quantum-pulse 2s ease-in-out infinite;
    transform-origin: center;
  }

  .neural-processing {
    position: relative;
    overflow: hidden;
  }

  .neural-processing::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(56, 189, 248, 0.3),
      transparent
    );
    animation: neural-scan 2s linear infinite;
  }

  .data-stream {
    background: linear-gradient(
      135deg,
      rgba(56, 189, 248, 0.1) 0%,
      rgba(147, 51, 234, 0.1) 50%,
      rgba(34, 197, 94, 0.1) 100%
    );
    animation: data-flow 4s ease-in-out infinite;
  }

  .hologram-text {
    text-shadow: 
      0 0 5px rgba(56, 189, 248, 0.5),
      0 0 10px rgba(56, 189, 248, 0.3),
      0 0 15px rgba(56, 189, 248, 0.2);
    animation: hologram-flicker 3s ease-in-out infinite;
  }

  .matrix-grid {
    background-image: 
      linear-gradient(rgba(56, 189, 248, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(56, 189, 248, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
    animation: matrix-scroll 10s linear infinite;
  }

  .energy-core {
    background: radial-gradient(
      circle at center,
      rgba(56, 189, 248, 0.8) 0%,
      rgba(154, 12, 12, 0.6) 30%,
      rgba(34, 197, 94, 0.4) 70%,
      transparent 100%
    );
    animation: energy-pulse 1.5s ease-in-out infinite;
  }

  /* Chat Message Enhancements */
  .chat-message {
    padding: 0.75rem;
    border-radius: 0.75rem;
    backdrop-filter: blur(4px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
  }

  .chat-message::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.1),
      transparent
    );
    transition: left 0.5s ease;
  }

  .chat-message:hover::before {
    left: 100%;
  }

  .ai-thinking {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    border-radius: 0.75rem;
    background: linear-gradient(to right, rgba(37, 99, 235, 0.1), rgba(147, 51, 234, 0.1));
    border: 1px solid rgba(59, 130, 246, 0.2);
    backdrop-filter: blur(4px);
    animation: thinking-pulse 2s ease-in-out infinite;
  }
}

/* Enhanced Keyframe Animations */
@keyframes cyber-scan {
  0% { background-position: 0 0; }
  100% { background-position: 20px 20px; }
}

@keyframes quantum-pulse {
  0%, 100% { 
    transform: scale(1);
    opacity: 1;
  }
  50% { 
    transform: scale(1.05);
    opacity: 0.8;
  }
}

@keyframes neural-scan {
  0% { left: -100%; }
  100% { left: 100%; }
}

@keyframes data-flow {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}

@keyframes hologram-flicker {
  0%, 100% { 
    opacity: 1;
    text-shadow: 
      0 0 5px rgba(56, 189, 248, 0.5),
      0 0 10px rgba(56, 189, 248, 0.3);
  }
  50% { 
    opacity: 0.8;
    text-shadow: 
      0 0 5px rgba(56, 189, 248, 0.8),
      0 0 10px rgba(56, 189, 248, 0.6),
      0 0 15px rgba(56, 189, 248, 0.4);
  }
}

@keyframes matrix-scroll {
  0% { background-position: 0 0; }
  100% { background-position: 20px 20px; }
}

@keyframes energy-pulse {
  0%, 100% { 
    transform: scale(1);
    opacity: 0.8;
  }
  50% { 
    transform: scale(1.1);
    opacity: 1;
  }
}

@keyframes thinking-pulse {
  0%, 100% { 
    box-shadow: 0 0 20px rgba(56, 189, 248, 0.3);
  }
  50% { 
    box-shadow: 0 0 30px rgba(56, 189, 248, 0.5);
  }
}

/* Futuristic Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(
    to bottom,
    rgb(0, 0, 0),
    rgba(167, 14, 14, 0.6)
  );
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.984),
    rgba(163, 23, 23, 0.8)
  );
}

/* Single White Border Line for Cards and Buttons */
.card,
[class*="card"],
.bg-card,
[class*="bg-card"],
button,
[role="button"],
.btn,
[class*="btn"],
.button,
[class*="button"] {
  border: 1px solid white !important;
}

/* Specific targeting for common UI components */
.bg-white,
.bg-gray-50,
.bg-gray-100,
.bg-slate-50,
.bg-slate-100,
.rounded,
.rounded-lg,
.rounded-md,
.rounded-xl,
.rounded-2xl,
.shadow,
.shadow-sm,
.shadow-md,
.shadow-lg,
.border {
  border: 1px solid white !important;
}

/* Enhanced borders for interactive elements */
button:hover,
[role="button"]:hover,
.btn:hover,
[class*="btn"]:hover,
.button:hover,
[class*="button"]:hover {
  border: 1px solid rgba(255, 255, 255, 0.9) !important;
}
