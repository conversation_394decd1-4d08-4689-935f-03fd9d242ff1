import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/hooks/use-toast";

type Profile = {
  id: string;
  full_name: string | null;
  email: string | null;
  role: string | null;
  department_id: string | null;
  created_at: string;
  updated_at: string;
};

// ============= TYPES =============
type APIResponse<T> = {
  data: T | null;
  error: Error | null;
  success: boolean;
  message?: string;
};

type HTTPMethod = 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';

// ============= ENHANCED API UTILITIES =============
import { errorHandler, ERROR_CODES, type ErrorContext } from './error-handler';
import { logger } from './logger';

class APIService {
  private async executeWithNotification<T>(
    operation: () => Promise<any>,
    method: HTTPMethod,
    resource: string,
    showNotification = true
  ): Promise<APIResponse<T>> {
    const startTime = performance.now();
    const operationId = `${method}_${resource}_${Date.now()}`;

    try {
      logger.debug('api', `Starting API operation: ${method} ${resource}`, {
        operationId,
        method,
        resource,
      });

      const result = await operation();
      const duration = performance.now() - startTime;

      if (result.error) {
        throw new Error(result.error.message || 'Operation failed');
      }

      const success = true;
      const message = this.getSuccessMessage(method, resource);

      // Log successful API call
      logger.logAPICall(method, resource, 200, duration, {
        operationId,
        dataSize: result.data ? JSON.stringify(result.data).length : 0,
      });

      if (showNotification && method !== 'GET') {
        toast({
          title: "✅ Success",
          description: message,
          variant: "default",
        });
      }

      return {
        data: result.data,
        error: null,
        success,
        message
      };
    } catch (error: any) {
      const duration = performance.now() - startTime;
      const errorMessage = error.message || 'An unexpected error occurred';

      // Determine error category and code
      let errorCode = ERROR_CODES.API_INVALID_REQUEST;
      let errorCategory: 'api' | 'database' | 'network' = 'api';

      const isDatabaseError = errorMessage.includes('column') ||
                             errorMessage.includes('relation') ||
                             errorMessage.includes('does not exist') ||
                             errorMessage.includes('connection') ||
                             errorMessage.includes('database');

      const isNetworkError = errorMessage.includes('fetch') ||
                            errorMessage.includes('network') ||
                            errorMessage.includes('timeout');

      if (isDatabaseError) {
        errorCode = ERROR_CODES.DB_QUERY_FAILED;
        errorCategory = 'database';
      } else if (isNetworkError) {
        errorCode = ERROR_CODES.NETWORK_CONNECTION_LOST;
        errorCategory = 'network';
      }

      // Create error context
      const context: ErrorContext = {
        function: `API_${method}`,
        metadata: {
          operationId,
          method,
          resource,
          duration,
          url: typeof window !== 'undefined' ? window.location.href : undefined,
        },
      };

      // Handle error through centralized error handler
      const appError = errorHandler.createError(
        errorCode,
        errorMessage,
        errorCategory,
        context,
        error
      );

      // Log API error
      logger.logAPICall(method, resource, error.status || 500, duration, {
        operationId,
        error: errorMessage,
        errorCode: appError.code,
        errorId: appError.id,
      });

      // Don't show notifications for database errors on API key operations
      // as we expect to fall back to demo mode
      const isAPIKeyOperation = resource.toLowerCase().includes('api key');

      if (showNotification && !isDatabaseError && !isAPIKeyOperation) {
        errorHandler.handleError(appError, context, true);
      } else {
        // Log without showing toast
        errorHandler.handleError(appError, context, false);
      }

      return {
        data: null,
        error: new Error(errorMessage),
        success: false,
        message: errorMessage
      };
    }
  }

  private getSuccessMessage(method: HTTPMethod, resource: string): string {
    const messages = {
      POST: `${resource} created successfully`,
      PUT: `${resource} updated successfully`,
      PATCH: `${resource} updated successfully`,
      DELETE: `${resource} deleted successfully`,
      GET: `${resource} retrieved successfully`
    };
    return messages[method];
  }

  // ============= ENHANCED AUTH API =============
  auth = {
  getCurrentUser: async () => {
      return this.executeWithNotification(
        () => supabase.auth.getUser(),
        'GET',
        'User session',
        false
      );
  },

  getUserProfile: async (userId: string) => {
      return this.executeWithNotification(
        () => supabase
      .from('profiles')
          .select(`*, department:departments(id, name, description)`)
      .eq('id', userId)
          .single(),
        'GET',
        'User profile',
        false
      );
  },

  signOut: async () => {
      return this.executeWithNotification(
        () => supabase.auth.signOut(),
        'POST',
        'Sign out'
      );
    },

    updateProfile: async (userId: string, userData: any) => {
      return this.executeWithNotification(
        () => supabase
          .from('profiles')
          .update(userData)
          .eq('id', userId)
          .select()
          .single(),
        'PATCH',
        'Profile'
      );
    }
  };

  // ============= ENHANCED USERS API =============
  users = {
    getAll: async () => {
      return this.executeWithNotification(
        () => supabase
      .from('profiles')
          .select(`*, department:departments(id, name, description)`)
          .order('created_at', { ascending: false }),
        'GET',
        'Users',
        false
      );
    },

    getById: async (userId: string) => {
      return this.executeWithNotification(
        () => supabase
          .from('profiles')
          .select(`*, department:departments(id, name, description)`)
          .eq('id', userId)
          .single(),
        'GET',
        'User',
        false
      );
    },

    create: async (userData: any) => {
      return this.executeWithNotification(
        () => supabase
      .from('profiles')
      .insert(userData)
      .select()
          .single(),
        'POST',
        'User'
      );
    },

    update: async (userId: string, userData: any) => {
      return this.executeWithNotification(
        () => supabase
      .from('profiles')
      .update(userData)
      .eq('id', userId)
      .select()
          .single(),
        'PUT',
        'User'
      );
    },

    delete: async (userId: string) => {
      return this.executeWithNotification(
        () => supabase
      .from('profiles')
      .delete()
          .eq('id', userId),
        'DELETE',
        'User'
      );
    },

    bulkUpdate: async (userIds: string[], userData: any) => {
      return this.executeWithNotification(
        () => supabase
          .from('profiles')
          .update(userData)
          .in('id', userIds)
          .select(),
        'PATCH',
        'Users (bulk operation)'
      );
    }
  };

  // ============= ENHANCED PROJECTS API =============
  projects = {
    getAll: async (filters?: { status?: string; managerId?: string }) => {
      return this.executeWithNotification(
        () => {
          let query = supabase
      .from('projects')
            .select(`*, manager:profiles!manager_id(id, full_name, email)`);
          
          if (filters?.status) query = query.eq('status', filters.status);
          if (filters?.managerId) query = query.eq('manager_id', filters.managerId);
          
          return query.order('created_at', { ascending: false });
        },
        'GET',
        'Projects',
        false
      );
    },

    getById: async (projectId: string) => {
      return this.executeWithNotification(
        () => supabase
      .from('projects')
          .select(`*, manager:profiles!manager_id(id, full_name, email), tasks(*)`)
      .eq('id', projectId)
          .single(),
        'GET',
        'Project',
        false
      );
    },

    create: async (projectData: any) => {
      return this.executeWithNotification(
        () => supabase
      .from('projects')
      .insert(projectData)
      .select()
          .single(),
        'POST',
        'Project'
      );
    },

    update: async (projectId: string, projectData: any) => {
      return this.executeWithNotification(
        () => supabase
      .from('projects')
      .update(projectData)
      .eq('id', projectId)
      .select()
          .single(),
        'PUT',
        'Project'
      );
    },

    delete: async (projectId: string) => {
      return this.executeWithNotification(
        () => supabase
          .from('projects')
          .delete()
          .eq('id', projectId),
        'DELETE',
        'Project'
      );
    },

    updateStatus: async (projectId: string, status: string) => {
      return this.executeWithNotification(
        () => supabase
          .from('projects')
          .update({ status, updated_at: new Date().toISOString() })
          .eq('id', projectId)
          .select()
          .single(),
        'PATCH',
        'Project status'
      );
    }
  };

  // ============= ENHANCED TASKS API =============
  tasks = {
    getAll: async (filters?: { assignedTo?: string; projectId?: string; status?: string }) => {
      return this.executeWithNotification(
        () => {
    let query = supabase
      .from('tasks')
      .select(`
        *,
        assigned_to:profiles!assigned_to_id(id, full_name, email),
        created_by:profiles!created_by_id(id, full_name, email),
        project:projects(id, name)
      `);

          if (filters?.assignedTo) query = query.eq('assigned_to_id', filters.assignedTo);
          if (filters?.projectId) query = query.eq('project_id', filters.projectId);
          if (filters?.status) query = query.eq('status', filters.status);

          return query.order('created_at', { ascending: false });
        },
        'GET',
        'Tasks',
        false
      );
    },

    create: async (taskData: any) => {
      return this.executeWithNotification(
        () => supabase
      .from('tasks')
      .insert(taskData)
      .select()
          .single(),
        'POST',
        'Task'
      );
    },

    update: async (taskId: string, taskData: any) => {
      return this.executeWithNotification(
        () => supabase
      .from('tasks')
      .update(taskData)
      .eq('id', taskId)
      .select()
          .single(),
        'PUT',
        'Task'
      );
    },

    delete: async (taskId: string) => {
      return this.executeWithNotification(
        () => supabase
          .from('tasks')
          .delete()
          .eq('id', taskId),
        'DELETE',
        'Task'
      );
    },

    updateStatus: async (taskId: string, status: string) => {
      return this.executeWithNotification(
        () => supabase
          .from('tasks')
          .update({ status, updated_at: new Date().toISOString() })
          .eq('id', taskId)
          .select()
          .single(),
        'PATCH',
        'Task status'
      );
    },

    bulkAssign: async (taskIds: string[], assigneeId: string) => {
      return this.executeWithNotification(
        () => supabase
          .from('tasks')
          .update({ assigned_to_id: assigneeId, updated_at: new Date().toISOString() })
          .in('id', taskIds)
          .select(),
        'PATCH',
        'Tasks (bulk assignment)'
      );
    }
  };

  // ============= ENHANCED REPORTS API =============
  reports = {
    battery: {
      getAll: async (filters?: { reporterId?: string; siteId?: string }) => {
        return this.executeWithNotification(
          () => {
    let query = supabase
      .from('battery_reports')
              .select(`*, reporter:profiles!reporter_id(id, full_name, email)`);

            if (filters?.reporterId) query = query.eq('reporter_id', filters.reporterId);
            if (filters?.siteId) query = query.eq('site_id', filters.siteId);

            return query.order('created_at', { ascending: false });
          },
          'GET',
          'Battery reports',
          false
        );
      },

      create: async (reportData: any) => {
        return this.executeWithNotification(
          () => supabase
      .from('battery_reports')
      .insert(reportData)
      .select()
            .single(),
          'POST',
          'Battery report'
        );
      },

      update: async (reportId: string, reportData: any) => {
        return this.executeWithNotification(
          () => supabase
            .from('battery_reports')
            .update(reportData)
            .eq('id', reportId)
            .select()
            .single(),
          'PUT',
          'Battery report'
        );
      },

      delete: async (reportId: string) => {
        return this.executeWithNotification(
          () => supabase
            .from('battery_reports')
            .delete()
            .eq('id', reportId),
          'DELETE',
          'Battery report'
        );
      }
    },

    telecom: {
      getAll: async (filters?: { reporterId?: string; siteId?: string }) => {
        return this.executeWithNotification(
          () => {
    let query = supabase
      .from('ct_power_reports')
              .select(`*, created_by:profiles!created_by(id, full_name, email)`);

            if (filters?.reporterId) query = query.eq('created_by', filters.reporterId);
            if (filters?.siteId) query = query.eq('site_id', filters.siteId);

            return query.order('created_at', { ascending: false });
          },
          'GET',
          'Telecom reports',
          false
        );
      },

      create: async (reportData: any) => {
        return this.executeWithNotification(
          () => supabase
            .from('ct_power_reports')
            .insert(reportData)
            .select()
            .single(),
          'POST',
          'Telecom report'
        );
      },

      update: async (reportId: string, reportData: any) => {
        return this.executeWithNotification(
          () => supabase
            .from('ct_power_reports')
            .update(reportData)
            .eq('id', reportId)
            .select()
            .single(),
          'PUT',
          'Telecom report'
        );
      }
    }
  };

  // ============= ENHANCED FINANCIAL API =============
  financial = {
    invoices: {
      getAll: async (filters?: { status?: string; clientId?: string }) => {
        return this.executeWithNotification(
          () => {
            let query = supabase
              .from('accounts_invoices')
              .select(`*, created_by:profiles!created_by(id, full_name, email)`);

            if (filters?.status) query = query.eq('payment_status', filters.status);
            if (filters?.clientId) query = query.eq('client_id', filters.clientId);

            return query.order('created_at', { ascending: false });
          },
          'GET',
          'Invoices',
          false
        );
      },

      create: async (invoiceData: any) => {
        return this.executeWithNotification(
          () => supabase
            .from('accounts_invoices')
            .insert(invoiceData)
            .select()
            .single(),
          'POST',
          'Invoice'
        );
      },

      update: async (invoiceId: string, invoiceData: any) => {
        return this.executeWithNotification(
          () => supabase
            .from('accounts_invoices')
            .update(invoiceData)
            .eq('id', invoiceId)
            .select()
            .single(),
          'PUT',
          'Invoice'
        );
      },

      updateStatus: async (invoiceId: string, status: string) => {
        return this.executeWithNotification(
          () => supabase
            .from('accounts_invoices')
            .update({ payment_status: status, updated_at: new Date().toISOString() })
            .eq('id', invoiceId)
            .select()
            .single(),
          'PATCH',
          'Invoice status'
        );
      }
    },

    expenses: {
      getAll: async (filters?: { userId?: string; category?: string }) => {
        return this.executeWithNotification(
          () => {
            let query = supabase
              .from('expenses')
              .select(`*, created_by:profiles!created_by(id, full_name, email)`);

            if (filters?.userId) query = query.eq('created_by', filters.userId);
            if (filters?.category) query = query.eq('category', filters.category);

            return query.order('created_at', { ascending: false });
          },
          'GET',
          'Expenses',
          false
        );
      },

      create: async (expenseData: any) => {
        return this.executeWithNotification(
          () => supabase
            .from('expenses')
            .insert(expenseData)
            .select()
            .single(),
          'POST',
          'Expense'
        );
      },

      update: async (expenseId: string, expenseData: any) => {
        return this.executeWithNotification(
          () => supabase
            .from('expenses')
            .update(expenseData)
            .eq('id', expenseId)
            .select()
            .single(),
          'PUT',
          'Expense'
        );
      },

      delete: async (expenseId: string) => {
        return this.executeWithNotification(
          () => supabase
            .from('expenses')
            .delete()
            .eq('id', expenseId),
          'DELETE',
          'Expense'
        );
      }
    }
  };

  // ============= ENHANCED DOCUMENTS API =============
  documents = {
    list: async (filters?: { folder_id?: string; category?: string; search?: string }) => {
      return this.executeWithNotification(
        () => {
          let query = supabase
            .from('documents')
      .select(`
        *,
              uploader:profiles!uploaded_by(id, full_name, email),
              folder:folders(id, name)
            `);

          if (filters?.folder_id) query = query.eq('folder_id', filters.folder_id);
          if (filters?.category && filters.category !== 'all') query = query.eq('category', filters.category);
          if (filters?.search) {
            query = query.or(`title.ilike.%${filters.search}%,file_name.ilike.%${filters.search}%`);
          }

          return query.order('created_at', { ascending: false });
        },
        'GET',
        'Documents',
        false
      );
    },

    getById: async (documentId: string) => {
      return this.executeWithNotification(
        () => supabase
          .from('documents')
          .select(`
            *,
            uploader:profiles!uploaded_by(id, full_name, email),
            folder:folders(id, name)
          `)
          .eq('id', documentId)
          .single(),
        'GET',
        'Document',
        false
      );
    },

    create: async (documentData: any) => {
      return this.executeWithNotification(
        () => supabase
          .from('documents')
          .insert(documentData)
      .select()
          .single(),
        'POST',
        'Document'
      );
    },

    update: async (documentId: string, documentData: any) => {
      return this.executeWithNotification(
        () => supabase
          .from('documents')
          .update(documentData)
          .eq('id', documentId)
          .select()
          .single(),
        'PUT',
        'Document'
      );
    },

    delete: async (documentId: string) => {
      return this.executeWithNotification(
        () => supabase
          .from('documents')
          .delete()
          .eq('id', documentId),
        'DELETE',
        'Document'
      );
    },

    getFolders: async () => {
      return this.executeWithNotification(
        () => supabase
          .from('folders')
          .select('*')
          .order('name', { ascending: true }),
        'GET',
        'Folders',
        false
      );
    },

    createFolder: async (folderData: any) => {
      return this.executeWithNotification(
        () => supabase
          .from('folders')
          .insert(folderData)
          .select()
          .single(),
        'POST',
        'Folder'
      );
    },

    updateFolder: async (folderId: string, folderData: any) => {
      return this.executeWithNotification(
        () => supabase
          .from('folders')
          .update(folderData)
          .eq('id', folderId)
          .select()
          .single(),
        'PUT',
        'Folder'
      );
    },

    deleteFolder: async (folderId: string) => {
      return this.executeWithNotification(
        () => supabase
          .from('folders')
          .delete()
          .eq('id', folderId),
        'DELETE',
        'Folder'
      );
    }
  };

  // ============= ENHANCED SYSTEM API =============
  system = {
    getDashboardStats: async () => {
      return this.executeWithNotification(
        async () => {
          const [users, projects, tasks, activities] = await Promise.all([
            supabase.from('profiles').select('id, role'),
            supabase.from('projects').select('id, status'),
            supabase.from('tasks').select('id, status, priority'),
            supabase.from('system_activities').select('*').limit(10)
          ]);

          return {
            data: {
              users: users.data || [],
              projects: projects.data || [],
              tasks: tasks.data || [],
              activities: activities.data || []
            },
            error: null
          };
        },
        'GET',
        'Dashboard stats',
        false
      );
    },

    getActivityLogs: async (limit = 10) => {
      return this.executeWithNotification(
        async () => {
          try {
            // Use manual join approach to avoid foreign key issues
            const { data: activitiesData, error: activitiesError } = await supabase
              .from('system_activities')
      .select('*')
              .order('created_at', { ascending: false })
              .limit(limit);
            
            if (activitiesError) {
              console.error('Activities query failed:', activitiesError);
              throw activitiesError;
            }
            
            // Get unique user IDs from activities
            const userIds = [...new Set(activitiesData?.map(a => a.user_id).filter(Boolean) || [])];
            
            // Get profiles for these user IDs if any exist
            let profilesData = [];
            if (userIds.length > 0) {
              const { data: profiles, error: profilesError } = await supabase
                .from('profiles')
                .select('id, full_name, email')
                .in('id', userIds);
              
              if (!profilesError && profiles) {
                profilesData = profiles;
              } else {
                console.warn('Profiles query failed:', profilesError);
              }
            }
            
            // Create a map of profiles for quick lookup
            const profilesMap = new Map();
            profilesData.forEach(profile => {
              profilesMap.set(profile.id, profile);
            });
            
            // Transform the data to match the expected format
            const transformedData = activitiesData?.map(activity => ({
              ...activity,
              action: activity.action || activity.type,
              user: activity.user_id ? profilesMap.get(activity.user_id) || null : null
            })) || [];
            
            return transformedData;
          } catch (error) {
            console.error('Error in getActivityLogs:', error);
            
            // Return fallback data with proper structure
            return [
              {
                id: '1',
                type: 'system_startup',
                action: 'system_startup',
                description: 'System initialized successfully',
                user_id: null,
                user: null,
                created_at: new Date().toISOString(),
                metadata: { version: '1.0.0' }
              },
              {
                id: '2',
                type: 'user_login',
                action: 'user_login',
                description: 'User logged in to the system',
                user_id: null,
                user: { id: 'demo', full_name: 'Demo User', email: '<EMAIL>' },
                created_at: new Date(Date.now() - 300000).toISOString(),
                metadata: { session_id: 'demo-session' }
              },
              {
                id: '3',
                type: 'data_sync',
                action: 'data_sync',
                description: 'Database synchronization completed',
                user_id: null,
                user: null,
                created_at: new Date(Date.now() - 600000).toISOString(),
                metadata: { records: 150 }
              }
            ];
          }
        },
        'GET',
        'Activity logs',
        false
      );
    },

    createActivity: async (activityData: any) => {
      return this.executeWithNotification(
        () => supabase
          .from('system_activities')
          .insert(activityData)
      .select()
          .single(),
        'POST',
        'Activity log',
        false
      );
    },

    // Helper function to populate sample activity data
    populateSampleActivities: async () => {
      return this.executeWithNotification(
        async () => {
          const { data: currentUser } = await supabase.auth.getUser();
          const userId = currentUser.user?.id;
          
          const sampleActivities = [
            {
              type: 'system_startup',
              description: 'System initialized successfully',
              user_id: null,
              metadata: { version: '1.0.0', timestamp: new Date().toISOString() }
            },
            {
              type: 'user_login',
              description: 'User logged in to the system',
              user_id: userId,
              metadata: { session_id: 'session-' + Math.random().toString(36).substr(2, 9) }
            },
            {
              type: 'data_sync',
              description: 'Database synchronization completed',
              user_id: null,
              metadata: { records_synced: 150, duration: '2.3s' }
            },
            {
              type: 'user_action',
              description: 'User accessed activity management page',
              user_id: userId,
              metadata: { page: 'activity-management', action: 'view' }
            },
            {
              type: 'system_maintenance',
              description: 'Database schema updated',
              user_id: null,
              metadata: { operation: 'migration', version: '20250102060000' }
            }
          ];
          
    const { data, error } = await supabase
            .from('system_activities')
            .insert(sampleActivities)
            .select();
          
    if (error) throw error;
    return data;
        },
        'POST',
        'Sample activity data',
        false
      );
    }
  };

  // ============= NOTIFICATION SYSTEM =============
  notifications = {
    // Send in-app notification
    sendInAppNotification: async (data: {
      recipientId: string;
      title: string;
      message: string;
      type: 'info' | 'success' | 'warning' | 'error';
      actionUrl?: string;
    }): Promise<APIResponse<any>> => {
      return this.executeWithNotification(
        () => supabase.from('notifications').insert([{
          recipient_id: data.recipientId,
          title: data.title,
          message: data.message,
          type: data.type,
          action_url: data.actionUrl,
          is_read: false,
        }]),
        "Notification sent successfully",
        "Failed to send notification"
      );
    },

    // Send leave request notification to managers
    sendLeaveRequestNotification: async (data: {
      leaveRequestId: string;
      employeeName: string;
      leaveType: string;
      startDate: string;
      endDate: string;
      reason: string;
    }): Promise<APIResponse<any>> => {
      try {
        // Get all managers
        const { data: managers, error: managersError } = await supabase
          .from('profiles')
          .select('id, full_name, email')
          .in('role', ['manager', 'admin']);

        if (managersError) throw managersError;

        // Send in-app notifications to all managers
        const notificationPromises = managers.map(manager => 
          supabase.from('notifications').insert([{
            recipient_id: manager.id,
            title: 'New Leave Request',
            message: `${data.employeeName} has submitted a leave request for ${data.leaveType} leave from ${data.startDate} to ${data.endDate}.`,
            type: 'info',
            action_url: '/dashboard/manager/leave',
            is_read: false,
          }])
        );

        await Promise.all(notificationPromises);

        // Send email notifications
        try {
          await supabase.functions.invoke('send-notification', {
            body: {
              type: 'leave_request_submitted',
              recipients: managers.map(m => m.email).filter(Boolean),
              data: {
                employeeName: data.employeeName,
                leaveType: data.leaveType,
                startDate: data.startDate,
                endDate: data.endDate,
                reason: data.reason,
                leaveRequestId: data.leaveRequestId,
              }
            }
          });
        } catch (emailError) {
          console.warn('Failed to send email notifications:', emailError);
          // Don't fail the whole operation if email fails
        }

        return {
          data: { success: true },
          error: null,
          success: true,
          message: "Leave request notifications sent successfully"
        };
      } catch (error: any) {
        return {
          data: null,
          error: error.message,
          success: false,
          message: "Failed to send leave request notifications"
        };
      }
    },

    // Send leave status update notification
    sendLeaveStatusNotification: async (data: {
      leaveRequestId: string;
      employeeId: string;
      employeeName: string;
      status: 'approved' | 'rejected';
      approverName: string;
      rejectionReason?: string;
    }): Promise<APIResponse<any>> => {
      try {
        const isApproved = data.status === 'approved';
        const title = isApproved ? 'Leave Request Approved' : 'Leave Request Rejected';
        const message = isApproved 
          ? `Your leave request has been approved by ${data.approverName}.`
          : `Your leave request has been rejected by ${data.approverName}. ${data.rejectionReason ? `Reason: ${data.rejectionReason}` : ''}`;

        // Send in-app notification
        await supabase.from('notifications').insert([{
          recipient_id: data.employeeId,
          title,
          message,
          type: isApproved ? 'success' : 'error',
          action_url: '/dashboard/staff/profile',
          is_read: false,
        }]);

        // Send email notification
        try {
          const { data: employee } = await supabase
            .from('profiles')
            .select('email')
            .eq('id', data.employeeId)
      .single();

          if (employee?.email) {
            await supabase.functions.invoke('send-notification', {
              body: {
                type: `leave_request_${data.status}`,
                recipients: [employee.email],
                data: {
                  employeeName: data.employeeName,
                  approverName: data.approverName,
                  rejectionReason: data.rejectionReason,
                  leaveRequestId: data.leaveRequestId,
                }
              }
            });
          }
        } catch (emailError) {
          console.warn('Failed to send email notification:', emailError);
        }

        return {
          data: { success: true },
          error: null,
          success: true,
          message: "Leave status notification sent successfully"
        };
      } catch (error: any) {
        return {
          data: null,
          error: error.message,
          success: false,
          message: "Failed to send leave status notification"
        };
      }
    },

    // Get user notifications
    getUserNotifications: async (userId: string, limit = 50): Promise<APIResponse<any[]>> => {
      return this.executeWithNotification(
        () => supabase
          .from('notifications')
          .select('*')
          .eq('recipient_id', userId)
          .order('created_at', { ascending: false })
          .limit(limit),
        null, // Don't show success toast for fetching
        "Failed to load notifications"
      );
    },

    // Mark notification as read
    markAsRead: async (notificationId: string): Promise<APIResponse<any>> => {
      return this.executeWithNotification(
        () => supabase
          .from('notifications')
          .update({ is_read: true })
          .eq('id', notificationId),
        null, // Don't show success toast
        "Failed to mark notification as read"
      );
    },

    // Mark all notifications as read
    markAllAsRead: async (userId: string): Promise<APIResponse<any>> => {
      return this.executeWithNotification(
        () => supabase
          .from('notifications')
          .update({ is_read: true })
          .eq('recipient_id', userId)
          .eq('is_read', false),
        "All notifications marked as read",
        "Failed to mark notifications as read"
      );
    },
  };

  leave = {
    // Submit leave request
    submitRequest: async (data: {
      leaveType: string;
      startDate: string;
      endDate: string;
      reason: string;
    }): Promise<APIResponse<any>> => {
      return this.executeWithNotification(
        () => supabase.from('leave_requests').insert([{
          leave_type: data.leaveType,
          start_date: data.startDate,
          end_date: data.endDate,
          reason: data.reason,
          status: 'pending',
        }]).select().single(),
        "Leave request submitted successfully",
        "Failed to submit leave request"
      );
    },

    // Get user's leave requests
    getUserRequests: async (userId: string): Promise<APIResponse<any[]>> => {
      return this.executeWithNotification(
        () => supabase
          .from('leave_requests')
          .select('*')
          .eq('user_id', userId)
          .order('created_at', { ascending: false }),
        null,
        "Failed to load leave requests"
      );
    },

    // Get all leave requests (for managers)
    getAllRequests: async (): Promise<APIResponse<any[]>> => {
      return this.executeWithNotification(
        () => supabase
          .from('leave_requests')
      .select(`
        *,
            profiles:user_id (
              full_name,
              email,
              department:department_id (name)
            )
          `)
          .order('created_at', { ascending: false }),
        null,
        "Failed to load leave requests"
      );
    },

    // Approve leave request
    approveRequest: async (requestId: string, approverId: string): Promise<APIResponse<any>> => {
      return this.executeWithNotification(
        () => supabase
          .from('leave_requests')
          .update({
            status: 'approved',
            approved_by: approverId,
            approved_at: new Date().toISOString(),
          })
          .eq('id', requestId)
          .select()
          .single(),
        "Leave request approved successfully",
        "Failed to approve leave request"
      );
    },

    // Reject leave request
    rejectRequest: async (requestId: string, approverId: string, rejectionReason: string): Promise<APIResponse<any>> => {
      return this.executeWithNotification(
        () => supabase
          .from('leave_requests')
          .update({
            status: 'rejected',
            approved_by: approverId,
            approved_at: new Date().toISOString(),
            rejection_reason: rejectionReason,
          })
          .eq('id', requestId)
          .select()
          .single(),
        "Leave request rejected",
        "Failed to reject leave request"
      );
    },

    // Get user's leave balances
    getUserBalances: async (userId: string): Promise<APIResponse<any[]>> => {
      return this.executeWithNotification(
        () => supabase
          .from('leave_balances')
          .select('*')
          .eq('user_id', userId)
          .eq('year', new Date().getFullYear()),
        null,
        "Failed to load leave balances"
      );
    },

    // Update leave balance
    updateBalance: async (userId: string, leaveType: string, data: {
      totalDays?: number;
      usedDays?: number;
    }): Promise<APIResponse<any>> => {
      const currentYear = new Date().getFullYear();
      
      return this.executeWithNotification(
        () => supabase
          .from('leave_balances')
          .upsert([{
            user_id: userId,
            leave_type: leaveType,
            year: currentYear,
            ...data,
          }], {
            onConflict: 'user_id,leave_type,year'
          }),
        "Leave balance updated successfully",
        "Failed to update leave balance"
      );
    },
  };

  // API Key Management endpoints
  apiKeys = {
    // Get all API keys
    getAllKeys: async (): Promise<APIResponse<any[]>> => {
      return this.executeWithNotification(
        async () => {
          const { data, error } = await supabase
            .from('api_keys')
            .select('*')
      .order('created_at', { ascending: false });
          
    if (error) throw error;
    return data;
        },
        'GET',
        'API keys',
        false
      );
  },

    // Get API key statistics
    getStatistics: async (): Promise<APIResponse<any>> => {
      return this.executeWithNotification(
        async () => {
    const { data, error } = await supabase
            .rpc('get_api_key_statistics');
          
    if (error) throw error;
          return data[0] || { total_keys: 0, active_keys: 0, expired_keys: 0, total_usage: 0, most_used_service: null };
        },
        'GET',
        'API key statistics',
        false
      );
    },

    // Create new API key
    createKey: async (keyData: any): Promise<APIResponse<any>> => {
      return this.executeWithNotification(
        async () => {
          // Encrypt the API key before storing
          const encryptedKey = await supabase
            .rpc('encrypt_api_key', { api_key: keyData.api_key });
          
          if (encryptedKey.error) throw encryptedKey.error;

    const { data, error } = await supabase
            .from('api_keys')
            .insert([{
              name: keyData.name,
              service_provider: keyData.service_provider,
              api_key_encrypted: encryptedKey.data,
              description: keyData.description,
              expires_at: keyData.expires_at,
              created_by: (await supabase.auth.getUser()).data.user?.id
            }])
            .select()
            .single();
          
    if (error) throw error;
    return data;
        },
        'POST',
        'API key',
        false
      );
    },

    // Update API key
    updateKey: async (id: string, keyData: any): Promise<APIResponse<any>> => {
      return this.executeWithNotification(
        async () => {
          const updateData: any = {
            name: keyData.name,
            service_provider: keyData.service_provider,
            description: keyData.description,
            expires_at: keyData.expires_at,
            status: keyData.status
          };

          // Only encrypt and update API key if it's being changed
          if (keyData.api_key && keyData.api_key.trim() !== '') {
            const encryptedKey = await supabase
              .rpc('encrypt_api_key', { api_key: keyData.api_key });
            
            if (encryptedKey.error) throw encryptedKey.error;
            updateData.api_key_encrypted = encryptedKey.data;
          }

    const { data, error } = await supabase
            .from('api_keys')
            .update(updateData)
            .eq('id', id)
      .select()
      .single();
          
    if (error) throw error;
    return data;
        },
        'PATCH',
        'API key',
        false
      );
    },

    // Delete API key
    deleteKey: async (id: string): Promise<APIResponse<void>> => {
      return this.executeWithNotification(
        async () => {
          const { error } = await supabase
            .from('api_keys')
            .delete()
            .eq('id', id);
          
          if (error) throw error;
          return undefined;
        },
        'DELETE',
        'API key',
        false
      );
    },

    // Get API key usage logs
    getUsageLogs: async (apiKeyId: string): Promise<APIResponse<any[]>> => {
      return this.executeWithNotification(
        async () => {
    const { data, error } = await supabase
            .from('api_key_usage_logs')
      .select('*')
            .eq('api_key_id', apiKeyId)
            .order('used_at', { ascending: false })
            .limit(100);
          
    if (error) throw error;
    return data;
        },
        'GET',
        'API key usage logs',
        false
      );
    },

    // Log API key usage
    logUsage: async (apiKeyId: string, usageData: any): Promise<APIResponse<void>> => {
      return this.executeWithNotification(
        async () => {
          const { error } = await supabase
            .from('api_key_usage_logs')
            .insert([{
              api_key_id: apiKeyId,
              endpoint: usageData.endpoint,
              method: usageData.method,
              status_code: usageData.status_code,
              response_time_ms: usageData.response_time_ms,
              error_message: usageData.error_message
            }]);
          
          if (error) throw error;

          // Update usage count
          const { error: updateError } = await supabase
            .from('api_keys')
            .update({ 
              usage_count: supabase.raw('usage_count + 1'),
              last_used_at: new Date().toISOString()
            })
            .eq('id', apiKeyId);
          
          if (updateError) throw updateError;
          
          return undefined;
        },
        'POST',
        'API key usage',
        false
      );
    },

    // Get API key by service provider
    getKeyByProvider: async (serviceProvider: string): Promise<APIResponse<any | null>> => {
      return this.executeWithNotification(
        async () => {
    const { data, error } = await supabase
            .from('api_keys')
            .select('*')
            .eq('service_provider', serviceProvider)
            .eq('status', 'active')
            .order('created_at', { ascending: false })
            .limit(1);
          
          if (error) throw error;
          return data[0] || null;
        },
        'GET',
        'API key by provider',
        false
      );
    },

    // Test API key connectivity
    testKey: async (id: string): Promise<APIResponse<any>> => {
      return this.executeWithNotification(
        async () => {
          const { data: apiKey, error } = await supabase
            .from('api_keys')
            .select('*')
            .eq('id', id)
      .single();
          
    if (error) throw error;
          
          // For now, just return success (in production, you'd test the actual API)
          return { success: true, message: 'API key is valid and active' };
        },
        'GET',
        'API key test',
        false
      );
    }
  };

  // Battery Management endpoints
  battery = {
    // Get all battery inventory
    getInventory: async (): Promise<APIResponse<any[]>> => {
      return this.executeWithNotification(
        async () => {
    const { data, error } = await supabase
            .from('battery_inventory')
            .select('*')
            .order('battery_id');
          
    if (error) throw error;
    return data;
        },
        'GET',
        'battery inventory',
        false
      );
  },

    // Get battery by ID
    getById: async (id: string): Promise<APIResponse<any>> => {
      return this.executeWithNotification(
        async () => {
    const { data, error } = await supabase
            .from('battery_inventory')
            .select('*')
            .eq('id', id)
      .single();
          
    if (error) throw error;
    return data;
        },
        'GET',
        'battery',
        false
      );
    },

    // Get battery statistics
    getStatistics: async (): Promise<APIResponse<any>> => {
      return this.executeWithNotification(
        async () => {
    const { data, error } = await supabase
            .rpc('get_battery_statistics');
          
    if (error) throw error;
          return data[0] || {
            total_batteries: 0,
            operational_batteries: 0,
            maintenance_batteries: 0,
            critical_batteries: 0,
            total_capacity_kwh: 0,
            average_charge_level: 0,
            pending_reports: 0,
            maintenance_due: 0
          };
        },
        'GET',
        'battery statistics',
        false
      );
    },

    // Submit battery report
    submitReport: async (reportData: {
      battery_id: string;
      charge_level: number;
      voltage_reading?: number;
      temperature?: number;
      status: string;
      maintenance_notes: string;
      issues_found?: string;
      corrective_actions?: string;
    }): Promise<APIResponse<any>> => {
      return this.executeWithNotification(
        async () => {
          const user = await supabase.auth.getUser();
          if (!user.data.user) throw new Error('User not authenticated');

    const { data, error } = await supabase
            .from('battery_reports')
            .insert([{
              ...reportData,
              reported_by: user.data.user.id,
              report_date: new Date().toISOString().split('T')[0],
              approval_status: 'pending'
            }])
      .select()
      .single();
          
    if (error) throw error;
    return data;
        },
        'POST',
        'battery report'
      );
    },

    // Get battery reports (filtered by user for staff, all for managers/admins)
    getReports: async (filters?: {
      userId?: string;
      batteryId?: string;
      status?: string;
      limit?: number;
    }): Promise<APIResponse<any[]>> => {
      return this.executeWithNotification(
        async () => {
    let query = supabase
            .from('battery_reports')
      .select(`
        *,
              battery_inventory!inner(battery_id, battery_model, location),
              reported_by_profile:profiles!reported_by(full_name, email)
      `);

    if (filters?.userId) {
            query = query.eq('reported_by', filters.userId);
          }
          if (filters?.batteryId) {
            query = query.eq('battery_id', filters.batteryId);
          }
          if (filters?.status) {
            query = query.eq('approval_status', filters.status);
          }

          query = query.order('created_at', { ascending: false });
          
          if (filters?.limit) {
            query = query.limit(filters.limit);
          }

          const { data, error } = await query;
          
    if (error) throw error;
    return data;
        },
        'GET',
        'battery reports',
        false
      );
    },

    // Approve battery report (managers/admins only)
    approveReport: async (reportId: string, approvalNotes?: string): Promise<APIResponse<any>> => {
      return this.executeWithNotification(
        async () => {
          const user = await supabase.auth.getUser();
          if (!user.data.user) throw new Error('User not authenticated');

    const { data, error } = await supabase
            .from('battery_reports')
            .update({
              approval_status: 'approved',
              approved_by: user.data.user.id,
              approval_date: new Date().toISOString(),
              approval_notes: approvalNotes
            })
            .eq('id', reportId)
      .select()
      .single();
          
    if (error) throw error;
    return data;
        },
        'PATCH',
        'battery report approval'
      );
    },

    // Reject battery report (managers/admins only)
    rejectReport: async (reportId: string, rejectionNotes: string): Promise<APIResponse<any>> => {
      return this.executeWithNotification(
        async () => {
          const user = await supabase.auth.getUser();
          if (!user.data.user) throw new Error('User not authenticated');

    const { data, error } = await supabase
            .from('battery_reports')
            .update({
              approval_status: 'rejected',
              approved_by: user.data.user.id,
              approval_date: new Date().toISOString(),
              approval_notes: rejectionNotes
            })
            .eq('id', reportId)
      .select()
      .single();
          
    if (error) throw error;
    return data;
        },
        'PATCH',
        'battery report rejection'
      );
    },

    // Add new battery to inventory
    addToInventory: async (batteryData: {
      battery_id: string;
      battery_model: string;
      manufacturer: string;
      capacity_kwh: number;
      voltage: number;
      installation_date: string;
      location: string;
      status?: string;
      warranty_expiry?: string;
      purchase_cost?: number;
    }): Promise<APIResponse<any>> => {
      return this.executeWithNotification(
        async () => {
    const { data, error } = await supabase
            .from('battery_inventory')
            .insert([{
              ...batteryData,
              status: batteryData.status || 'operational'
            }])
            .select()
            .single();
          
    if (error) throw error;
    return data;
        },
        'POST',
        'battery inventory'
      );
  },

    // Update battery in inventory
    updateInventory: async (id: string, batteryData: any): Promise<APIResponse<any>> => {
      return this.executeWithNotification(
        async () => {
    const { data, error } = await supabase
            .from('battery_inventory')
            .update(batteryData)
            .eq('id', id)
      .select()
      .single();
          
    if (error) throw error;
    return data;
        },
        'PATCH',
        'battery inventory'
      );
    },

    // Delete battery from inventory
    deleteFromInventory: async (id: string): Promise<APIResponse<void>> => {
      return this.executeWithNotification(
        async () => {
          const { error } = await supabase
            .from('battery_inventory')
            .delete()
            .eq('id', id);
          
          if (error) throw error;
          return undefined;
        },
        'DELETE',
        'battery inventory'
      );
    },

    // Get maintenance schedule
    getMaintenanceSchedule: async (batteryId?: string): Promise<APIResponse<any[]>> => {
      return this.executeWithNotification(
        async () => {
      let query = supabase
            .from('battery_maintenance_schedule')
            .select(`
              *,
              battery_inventory!inner(battery_id, battery_model, location),
              assigned_to_profile:profiles!assigned_to(full_name, email)
            `);

          if (batteryId) {
            query = query.eq('battery_id', batteryId);
          }

          query = query.order('scheduled_date', { ascending: true });

      const { data, error } = await query;
          
      if (error) throw error;
          return data;
        },
        'GET',
        'maintenance schedule',
        false
      );
    },

    // Schedule maintenance
    scheduleMaintenance: async (maintenanceData: {
      battery_id: string;
      maintenance_type: string;
      scheduled_date: string;
      assigned_to?: string;
      notes?: string;
    }): Promise<APIResponse<any>> => {
      return this.executeWithNotification(
        async () => {
      const { data, error } = await supabase
            .from('battery_maintenance_schedule')
            .insert([{
              ...maintenanceData,
              status: 'scheduled'
            }])
            .select()
        .single();
          
      if (error) throw error;
      return data;
        },
        'POST',
        'maintenance schedule'
      );
    },

    // Record performance metrics
    recordPerformance: async (metricsData: {
      battery_id: string;
      charge_cycles?: number;
      efficiency_percentage?: number;
      capacity_degradation?: number;
      average_temperature?: number;
      power_output_kw?: number;
      energy_stored_kwh?: number;
      energy_discharged_kwh?: number;
    }): Promise<APIResponse<any>> => {
      return this.executeWithNotification(
        async () => {
          const user = await supabase.auth.getUser();
          
      const { data, error } = await supabase
            .from('battery_performance_metrics')
            .insert([{
              ...metricsData,
              metric_date: new Date().toISOString().split('T')[0],
              recorded_by: user.data.user?.id
            }])
        .select()
        .single();
          
      if (error) throw error;
      return data;
    },
        'POST',
        'performance metrics'
      );
    },

    // Get performance metrics
    getPerformanceMetrics: async (batteryId: string, days?: number): Promise<APIResponse<any[]>> => {
      return this.executeWithNotification(
        async () => {
          let query = supabase
            .from('battery_performance_metrics')
        .select('*')
            .eq('battery_id', batteryId);

          if (days) {
            const fromDate = new Date();
            fromDate.setDate(fromDate.getDate() - days);
            query = query.gte('metric_date', fromDate.toISOString().split('T')[0]);
          }

          query = query.order('metric_date', { ascending: false });

          const { data, error } = await query;
          
      if (error) throw error;
      return data;
        },
        'GET',
        'performance metrics',
        false
      );
    }
  };

  // Route-based API call handler
  async handleRoute(path: string, method: string = 'GET', data?: any): Promise<APIResponse<any>> {
    const endpoint = `${method} ${path}`;
    
    // Handle API key endpoints
    if (path.startsWith('/api/admin/api-keys')) {
      if (method === 'GET' && path === '/api/admin/api-keys') {
        return this.apiKeys.getAllKeys();
      } else if (method === 'GET' && path === '/api/admin/api-keys/statistics') {
        return this.apiKeys.getStatistics();
      } else if (method === 'POST' && path === '/api/admin/api-keys') {
        return this.apiKeys.createKey(data);
      } else if (method === 'PATCH' && path.match(/^\/api\/admin\/api-keys\/[^/]+$/)) {
        const id = path.split('/').pop()!;
        return this.apiKeys.updateKey(id, data);
      } else if (method === 'DELETE' && path.match(/^\/api\/admin\/api-keys\/[^/]+$/)) {
        const id = path.split('/').pop()!;
        return this.apiKeys.deleteKey(id);
      } else if (method === 'GET' && path.match(/^\/api\/admin\/api-keys\/[^/]+\/usage$/)) {
        const id = path.split('/')[4];
        return this.apiKeys.getUsageLogs(id);
      } else if (method === 'POST' && path.match(/^\/api\/admin\/api-keys\/[^/]+\/usage$/)) {
        const id = path.split('/')[4];
        return this.apiKeys.logUsage(id, data);
      } else if (method === 'GET' && path.match(/^\/api\/admin\/api-keys\/provider\/[^/]+$/)) {
        const provider = path.split('/').pop()!;
        return this.apiKeys.getKeyByProvider(provider);
      } else if (method === 'POST' && path.match(/^\/api\/admin\/api-keys\/[^/]+\/test$/)) {
        const id = path.split('/')[4];
        return this.apiKeys.testKey(id);
      }
    }
    
    if (this.endpoints[endpoint]) {
      return this.endpoints[endpoint](data);
    }
    
    // Handle parameterized routes
    for (const [pattern, handler] of Object.entries(this.endpoints)) {
      if (this.matchRoute(pattern, endpoint)) {
        return handler(data);
      }
    }
    
    throw new Error(`No handler found for ${endpoint}`);
  }
}

// Create and export the enhanced API instance
export const api = new APIService();

// Export legacy API for backward compatibility
export {
  authAPI as auth,
  usersAPI as users,
  departmentsAPI as departments,
  projectsAPI as projects,
  tasksAPI as tasks,
  reportsAPI as reports,
  telecomSitesAPI as telecomSites,
  memosAPI as memos,
  financialAPI as financial,
  fleetAPI as fleet,
  assetsAPI as assets,
  constructionAPI as construction,
  timeTrackingAPI as timeTracking
} from './api-legacy';

// API Key Management TypeScript interfaces
export interface APIKey {
  id: string;
  name: string;
  service_provider: string;
  api_key_encrypted: string;
  description: string;
  status: 'active' | 'inactive' | 'expired';
  usage_count: number;
  last_used_at: string | null;
  expires_at: string | null;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface APIKeyStatistics {
  total_keys: number;
  active_keys: number;
  expired_keys: number;
  total_usage: number;
  most_used_service: string | null;
}

export interface APIKeyUsageLog {
  id: string;
  api_key_id: string;
  endpoint: string;
  method: string;
  status_code: number;
  response_time_ms: number;
  error_message: string | null;
  used_at: string;
}

// Update the main API object to include API key management endpoints
api.admin = {
  ...api.admin,
  apiKeys: {
    get: api.apiKeys.getAllKeys,
    statistics: api.apiKeys.getStatistics,
    create: api.apiKeys.createKey,
    update: api.apiKeys.updateKey,
    delete: api.apiKeys.deleteKey,
    getUsage: api.apiKeys.getUsageLogs,
    logUsage: api.apiKeys.logUsage,
    getByProvider: api.apiKeys.getKeyByProvider,
    test: api.apiKeys.testKey
  }
};

// Update API endpoints map
api.endpoints = {
  ...api.endpoints,
  // API Key Management
  'GET /api/admin/api-keys': api.apiKeys.getAllKeys,
  'GET /api/admin/api-keys/statistics': api.apiKeys.getStatistics,
  'POST /api/admin/api-keys': api.apiKeys.createKey,
  'PATCH /api/admin/api-keys/:id': api.apiKeys.updateKey,
  'DELETE /api/admin/api-keys/:id': api.apiKeys.deleteKey,
  'GET /api/admin/api-keys/:id/usage': api.apiKeys.getUsageLogs,
  'POST /api/admin/api-keys/:id/usage': api.apiKeys.logUsage,
  'GET /api/admin/api-keys/provider/:provider': api.apiKeys.getKeyByProvider,
  'POST /api/admin/api-keys/:id/test': api.apiKeys.testKey
};