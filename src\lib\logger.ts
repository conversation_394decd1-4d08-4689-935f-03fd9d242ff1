/**
 * Comprehensive Logging System
 * Structured logging with multiple levels, contexts, and output targets
 */

export type LogLevel = 'debug' | 'info' | 'warn' | 'error' | 'fatal';
export type LogCategory = 
  | 'auth' 
  | 'api' 
  | 'database' 
  | 'ui' 
  | 'performance' 
  | 'security' 
  | 'integration' 
  | 'system'
  | 'user-action'
  | 'business-logic';

export interface LogEntry {
  id: string;
  timestamp: Date;
  level: LogLevel;
  category: LogCategory;
  message: string;
  context?: Record<string, any>;
  userId?: string;
  sessionId?: string;
  requestId?: string;
  component?: string;
  function?: string;
  duration?: number;
  metadata?: Record<string, any>;
  stack?: string;
}

export interface LoggerConfig {
  level: LogLevel;
  enableConsole: boolean;
  enableStorage: boolean;
  enableRemote: boolean;
  maxStorageSize: number;
  remoteEndpoint?: string;
  enablePerformanceLogging: boolean;
  enableUserActionLogging: boolean;
  enableSecurityLogging: boolean;
}

class Logger {
  private static instance: Logger;
  private config: LoggerConfig;
  private logs: LogEntry[] = [];
  private logLevels: Record<LogLevel, number> = {
    debug: 0,
    info: 1,
    warn: 2,
    error: 3,
    fatal: 4,
  };

  private constructor(config?: Partial<LoggerConfig>) {
    this.config = {
      level: 'info',
      enableConsole: true,
      enableStorage: true,
      enableRemote: false,
      maxStorageSize: 1000,
      enablePerformanceLogging: true,
      enableUserActionLogging: true,
      enableSecurityLogging: true,
      ...config,
    };

    // Set log level based on environment
    if (process.env.NODE_ENV === 'development') {
      this.config.level = 'debug';
    } else if (process.env.NODE_ENV === 'production') {
      this.config.level = 'warn';
    }
  }

  static getInstance(config?: Partial<LoggerConfig>): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger(config);
    }
    return Logger.instance;
  }

  // Core logging method
  private log(
    level: LogLevel,
    category: LogCategory,
    message: string,
    context?: Record<string, any>,
    metadata?: Record<string, any>
  ): void {
    // Check if log level meets threshold
    if (this.logLevels[level] < this.logLevels[this.config.level]) {
      return;
    }

    const logEntry: LogEntry = {
      id: this.generateLogId(),
      timestamp: new Date(),
      level,
      category,
      message,
      context,
      metadata,
      userId: this.getCurrentUserId(),
      sessionId: this.getSessionId(),
      requestId: this.getRequestId(),
      component: context?.component,
      function: context?.function,
      duration: context?.duration,
      stack: level === 'error' || level === 'fatal' ? new Error().stack : undefined,
    };

    // Store log entry
    if (this.config.enableStorage) {
      this.storeLog(logEntry);
    }

    // Console output
    if (this.config.enableConsole) {
      this.outputToConsole(logEntry);
    }

    // Remote logging
    if (this.config.enableRemote && this.config.remoteEndpoint) {
      this.sendToRemote(logEntry);
    }
  }

  // Public logging methods
  debug(category: LogCategory, message: string, context?: Record<string, any>): void {
    this.log('debug', category, message, context);
  }

  info(category: LogCategory, message: string, context?: Record<string, any>): void {
    this.log('info', category, message, context);
  }

  warn(category: LogCategory, message: string, context?: Record<string, any>): void {
    this.log('warn', category, message, context);
  }

  error(category: LogCategory, message: string, context?: Record<string, any>): void {
    this.log('error', category, message, context);
  }

  fatal(category: LogCategory, message: string, context?: Record<string, any>): void {
    this.log('fatal', category, message, context);
  }

  // Specialized logging methods
  logUserAction(action: string, details?: Record<string, any>): void {
    if (!this.config.enableUserActionLogging) return;
    
    this.info('user-action', `User action: ${action}`, {
      action,
      details,
      timestamp: new Date().toISOString(),
      url: typeof window !== 'undefined' ? window.location.href : undefined,
    });
  }

  logPerformance(operation: string, duration: number, details?: Record<string, any>): void {
    if (!this.config.enablePerformanceLogging) return;
    
    const level: LogLevel = duration > 1000 ? 'warn' : duration > 500 ? 'info' : 'debug';
    
    this.log('performance', `Performance: ${operation}`, {
      operation,
      duration,
      details,
      performanceThreshold: duration > 1000 ? 'slow' : duration > 500 ? 'medium' : 'fast',
    });
  }

  logSecurity(event: string, details?: Record<string, any>): void {
    if (!this.config.enableSecurityLogging) return;
    
    this.warn('security', `Security event: ${event}`, {
      event,
      details,
      timestamp: new Date().toISOString(),
      userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : undefined,
      ip: details?.ip || 'unknown',
    });
  }

  logAPICall(
    method: string,
    url: string,
    status: number,
    duration: number,
    details?: Record<string, any>
  ): void {
    const level: LogLevel = status >= 500 ? 'error' : status >= 400 ? 'warn' : 'info';
    
    this.log('api', `API ${method} ${url}`, {
      method,
      url,
      status,
      duration,
      details,
      statusCategory: status >= 500 ? 'server-error' : status >= 400 ? 'client-error' : 'success',
    });
  }

  logDatabaseQuery(
    query: string,
    duration: number,
    rowCount?: number,
    details?: Record<string, any>
  ): void {
    const level: LogLevel = duration > 1000 ? 'warn' : 'debug';
    
    this.log('database', `Database query executed`, {
      query: query.substring(0, 100) + (query.length > 100 ? '...' : ''),
      duration,
      rowCount,
      details,
      performanceCategory: duration > 1000 ? 'slow' : duration > 500 ? 'medium' : 'fast',
    });
  }

  logAuthentication(event: string, success: boolean, details?: Record<string, any>): void {
    const level: LogLevel = success ? 'info' : 'warn';
    
    this.log('auth', `Authentication: ${event}`, {
      event,
      success,
      details,
      timestamp: new Date().toISOString(),
    });
  }

  logIntegration(
    service: string,
    operation: string,
    success: boolean,
    details?: Record<string, any>
  ): void {
    const level: LogLevel = success ? 'info' : 'error';
    
    this.log('integration', `Integration ${service}: ${operation}`, {
      service,
      operation,
      success,
      details,
    });
  }

  // Performance timing utilities
  startTimer(operation: string): () => void {
    const startTime = performance.now();
    
    return () => {
      const duration = performance.now() - startTime;
      this.logPerformance(operation, duration);
      return duration;
    };
  }

  // Async operation wrapper with logging
  async withLogging<T>(
    operation: () => Promise<T>,
    operationName: string,
    category: LogCategory = 'system'
  ): Promise<T> {
    const startTime = performance.now();
    
    try {
      this.debug(category, `Starting operation: ${operationName}`);
      const result = await operation();
      const duration = performance.now() - startTime;
      
      this.info(category, `Operation completed: ${operationName}`, { duration });
      this.logPerformance(operationName, duration);
      
      return result;
    } catch (error) {
      const duration = performance.now() - startTime;
      
      this.error(category, `Operation failed: ${operationName}`, {
        error: error instanceof Error ? error.message : String(error),
        duration,
        stack: error instanceof Error ? error.stack : undefined,
      });
      
      throw error;
    }
  }

  // Storage management
  private storeLog(logEntry: LogEntry): void {
    this.logs.unshift(logEntry);
    
    // Maintain storage size limit
    if (this.logs.length > this.config.maxStorageSize) {
      this.logs = this.logs.slice(0, this.config.maxStorageSize);
    }
  }

  // Console output with formatting
  private outputToConsole(logEntry: LogEntry): void {
    const timestamp = logEntry.timestamp.toISOString();
    const prefix = this.getLogPrefix(logEntry.level, logEntry.category);
    const message = `${timestamp} ${prefix} ${logEntry.message}`;
    
    const consoleMethod = this.getConsoleMethod(logEntry.level);
    
    if (logEntry.context || logEntry.metadata) {
      consoleMethod(message, {
        context: logEntry.context,
        metadata: logEntry.metadata,
        userId: logEntry.userId,
        component: logEntry.component,
      });
    } else {
      consoleMethod(message);
    }
  }

  // Remote logging
  private async sendToRemote(logEntry: LogEntry): Promise<void> {
    if (!this.config.remoteEndpoint) return;
    
    try {
      await fetch(this.config.remoteEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(logEntry),
      });
    } catch (error) {
      // Fallback to console if remote logging fails
      console.error('Failed to send log to remote endpoint:', error);
    }
  }

  // Utility methods
  private generateLogId(): string {
    return `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private getCurrentUserId(): string | undefined {
    // In a real app, get from auth context
    return typeof window !== 'undefined' 
      ? localStorage.getItem('userId') || undefined
      : undefined;
  }

  private getSessionId(): string | undefined {
    return typeof window !== 'undefined' 
      ? sessionStorage.getItem('sessionId') || undefined
      : undefined;
  }

  private getRequestId(): string | undefined {
    // Generate or retrieve request ID for tracing
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;
  }

  private getLogPrefix(level: LogLevel, category: LogCategory): string {
    const levelEmojis = {
      debug: '🔍',
      info: 'ℹ️',
      warn: '⚠️',
      error: '❌',
      fatal: '🚨',
    };
    
    const categoryEmojis = {
      auth: '🔐',
      api: '🌐',
      database: '🗄️',
      ui: '🖥️',
      performance: '⚡',
      security: '🛡️',
      integration: '🔗',
      system: '⚙️',
      'user-action': '👤',
      'business-logic': '💼',
    };
    
    return `${levelEmojis[level]} [${level.toUpperCase()}] ${categoryEmojis[category]} [${category.toUpperCase()}]`;
  }

  private getConsoleMethod(level: LogLevel): (...args: any[]) => void {
    switch (level) {
      case 'debug':
        return console.debug;
      case 'info':
        return console.info;
      case 'warn':
        return console.warn;
      case 'error':
      case 'fatal':
        return console.error;
      default:
        return console.log;
    }
  }

  // Public methods for accessing logs
  getLogs(filters?: {
    level?: LogLevel;
    category?: LogCategory;
    startTime?: Date;
    endTime?: Date;
    userId?: string;
  }): LogEntry[] {
    let filteredLogs = [...this.logs];
    
    if (filters) {
      if (filters.level) {
        filteredLogs = filteredLogs.filter(log => log.level === filters.level);
      }
      if (filters.category) {
        filteredLogs = filteredLogs.filter(log => log.category === filters.category);
      }
      if (filters.startTime) {
        filteredLogs = filteredLogs.filter(log => log.timestamp >= filters.startTime!);
      }
      if (filters.endTime) {
        filteredLogs = filteredLogs.filter(log => log.timestamp <= filters.endTime!);
      }
      if (filters.userId) {
        filteredLogs = filteredLogs.filter(log => log.userId === filters.userId);
      }
    }
    
    return filteredLogs;
  }

  getLogStats(): {
    total: number;
    byLevel: Record<LogLevel, number>;
    byCategory: Record<LogCategory, number>;
  } {
    const stats = {
      total: this.logs.length,
      byLevel: {} as Record<LogLevel, number>,
      byCategory: {} as Record<LogCategory, number>,
    };
    
    // Initialize counters
    Object.keys(this.logLevels).forEach(level => {
      stats.byLevel[level as LogLevel] = 0;
    });
    
    // Count logs
    this.logs.forEach(log => {
      stats.byLevel[log.level]++;
      stats.byCategory[log.category] = (stats.byCategory[log.category] || 0) + 1;
    });
    
    return stats;
  }

  clearLogs(): void {
    this.logs = [];
  }

  updateConfig(config: Partial<LoggerConfig>): void {
    this.config = { ...this.config, ...config };
  }
}

// Export singleton instance
export const logger = Logger.getInstance();

// Convenience functions
export const logDebug = (category: LogCategory, message: string, context?: Record<string, any>) => 
  logger.debug(category, message, context);

export const logInfo = (category: LogCategory, message: string, context?: Record<string, any>) => 
  logger.info(category, message, context);

export const logWarn = (category: LogCategory, message: string, context?: Record<string, any>) => 
  logger.warn(category, message, context);

export const logError = (category: LogCategory, message: string, context?: Record<string, any>) => 
  logger.error(category, message, context);

export const logUserAction = (action: string, details?: Record<string, any>) => 
  logger.logUserAction(action, details);

export const logPerformance = (operation: string, duration: number, details?: Record<string, any>) => 
  logger.logPerformance(operation, duration, details);

export const withLogging = <T>(
  operation: () => Promise<T>,
  operationName: string,
  category: LogCategory = 'system'
): Promise<T> => logger.withLogging(operation, operationName, category);
