/**
 * Performance Configuration
 * Centralized configuration for performance optimizations
 */

// Query cache configuration for different data types
export const QUERY_CACHE_CONFIG = {
  // Static data that rarely changes
  static: {
    staleTime: 30 * 60 * 1000, // 30 minutes
    cacheTime: 60 * 60 * 1000, // 1 hour
    refetchOnWindowFocus: false,
  },
  
  // Semi-static data (departments, roles, etc.)
  semiStatic: {
    staleTime: 15 * 60 * 1000, // 15 minutes
    cacheTime: 30 * 60 * 1000, // 30 minutes
    refetchOnWindowFocus: false,
  },
  
  // Dynamic data (tasks, projects, etc.)
  dynamic: {
    staleTime: 2 * 60 * 1000, // 2 minutes
    cacheTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  },
  
  // Real-time data (notifications, activities)
  realTime: {
    staleTime: 30 * 1000, // 30 seconds
    cacheTime: 2 * 60 * 1000, // 2 minutes
    refetchOnWindowFocus: true,
  },
  
  // User-specific data
  userSpecific: {
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
  }
};

// Pagination configuration
export const PAGINATION_CONFIG = {
  defaultPageSize: 20,
  maxPageSize: 100,
  dashboardLimit: 10,
  searchLimit: 50,
  infiniteScrollThreshold: 5, // Load more when 5 items from bottom
};

// Performance thresholds
export const PERFORMANCE_THRESHOLDS = {
  queryTimeout: 30000, // 30 seconds
  fastQuery: 100, // < 100ms
  slowQuery: 1000, // > 1000ms
  criticalQuery: 3000, // > 3000ms
  maxRetries: 3,
  retryDelay: 1000,
};

// Bundle optimization settings
export const BUNDLE_CONFIG = {
  chunkSizeWarningLimit: 1000, // KB
  enableCodeSplitting: true,
  enableTreeShaking: true,
  enableMinification: true,
  enableGzip: true,
};

// Memory management settings
export const MEMORY_CONFIG = {
  maxCacheSize: 50 * 1024 * 1024, // 50MB
  gcInterval: 5 * 60 * 1000, // 5 minutes
  maxSubscriptions: 10,
  subscriptionCleanupDelay: 1000,
};

// API optimization settings
export const API_CONFIG = {
  batchRequests: true,
  batchDelay: 50, // ms
  maxBatchSize: 10,
  enableCompression: true,
  enableCaching: true,
  defaultTimeout: 15000, // 15 seconds
};

// Database query optimization
export const DB_QUERY_CONFIG = {
  defaultLimit: 50,
  maxLimit: 1000,
  enableIndexHints: true,
  enableQueryPlan: false, // Enable in development
  enableSlowQueryLog: false, // Enable in development
};

// Image and asset optimization
export const ASSET_CONFIG = {
  enableLazyLoading: true,
  enableWebP: true,
  enableImageOptimization: true,
  maxImageSize: 2 * 1024 * 1024, // 2MB
  thumbnailSize: 200, // pixels
  enableCDN: false, // Configure for production
};

// Real-time subscription optimization
export const SUBSCRIPTION_CONFIG = {
  maxConnections: 5,
  reconnectDelay: 1000,
  maxReconnectAttempts: 5,
  heartbeatInterval: 30000, // 30 seconds
  enableBatching: true,
  batchInterval: 100, // ms
};

// Performance monitoring configuration
export const MONITORING_CONFIG = {
  enablePerformanceTracking: true,
  enableErrorTracking: true,
  enableUserTiming: true,
  sampleRate: 0.1, // 10% sampling
  enableWebVitals: true,
  enableResourceTiming: true,
};

// Cache invalidation strategies
export const CACHE_INVALIDATION = {
  // Invalidate cache when these mutations occur
  mutations: {
    createTask: ['tasks', 'dashboard-data', 'user-tasks'],
    updateTask: ['tasks', 'dashboard-data', 'user-tasks'],
    deleteTask: ['tasks', 'dashboard-data', 'user-tasks'],
    createProject: ['projects', 'dashboard-data', 'user-projects'],
    updateProject: ['projects', 'dashboard-data', 'user-projects'],
    deleteProject: ['projects', 'dashboard-data', 'user-projects'],
    createMemo: ['memos', 'dashboard-data', 'department-memos'],
    updateProfile: ['profiles', 'user-profile', 'dashboard-data'],
  },
  
  // Time-based invalidation
  schedules: {
    dailyStats: '0 0 * * *', // Daily at midnight
    weeklyReports: '0 0 * * 0', // Weekly on Sunday
    monthlyMetrics: '0 0 1 * *', // Monthly on 1st
  }
};

// Query key factories for consistent caching
export const QUERY_KEYS = {
  // User-related queries
  user: {
    profile: (userId?: string) => ['user', 'profile', userId],
    preferences: (userId?: string) => ['user', 'preferences', userId],
    notifications: (userId?: string) => ['user', 'notifications', userId],
  },
  
  // Task-related queries
  tasks: {
    all: (filters?: any) => ['tasks', 'list', filters],
    byUser: (userId: string, filters?: any) => ['tasks', 'user', userId, filters],
    byProject: (projectId: string, filters?: any) => ['tasks', 'project', projectId, filters],
    detail: (taskId: string) => ['tasks', 'detail', taskId],
  },
  
  // Project-related queries
  projects: {
    all: (filters?: any) => ['projects', 'list', filters],
    byManager: (managerId: string, filters?: any) => ['projects', 'manager', managerId, filters],
    detail: (projectId: string) => ['projects', 'detail', projectId],
  },
  
  // Dashboard queries
  dashboard: {
    data: (role?: string, userId?: string) => ['dashboard', 'data', role, userId],
    stats: (role?: string) => ['dashboard', 'stats', role],
    activities: (limit?: number) => ['dashboard', 'activities', limit],
  },
  
  // System queries
  system: {
    departments: () => ['system', 'departments'],
    roles: () => ['system', 'roles'],
    activities: (limit?: number) => ['system', 'activities', limit],
    health: () => ['system', 'health'],
  }
};

// Performance optimization utilities
export const PERFORMANCE_UTILS = {
  // Debounce function for search queries
  debounce: <T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): ((...args: Parameters<T>) => void) => {
    let timeout: NodeJS.Timeout;
    return (...args: Parameters<T>) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func(...args), wait);
    };
  },
  
  // Throttle function for scroll events
  throttle: <T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): ((...args: Parameters<T>) => void) => {
    let inThrottle: boolean;
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func(...args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  },
  
  // Memoization utility
  memoize: <T extends (...args: any[]) => any>(fn: T): T => {
    const cache = new Map();
    return ((...args: any[]) => {
      const key = JSON.stringify(args);
      if (cache.has(key)) {
        return cache.get(key);
      }
      const result = fn(...args);
      cache.set(key, result);
      return result;
    }) as T;
  },
  
  // Batch function calls
  batch: <T>(
    fn: (items: T[]) => Promise<any>,
    delay: number = 50
  ) => {
    let items: T[] = [];
    let timeout: NodeJS.Timeout;
    
    return (item: T): Promise<any> => {
      return new Promise((resolve, reject) => {
        items.push(item);
        
        clearTimeout(timeout);
        timeout = setTimeout(async () => {
          try {
            const result = await fn([...items]);
            items = [];
            resolve(result);
          } catch (error) {
            items = [];
            reject(error);
          }
        }, delay);
      });
    };
  }
};

// Export default configuration
export const PERFORMANCE_CONFIG = {
  queryCache: QUERY_CACHE_CONFIG,
  pagination: PAGINATION_CONFIG,
  thresholds: PERFORMANCE_THRESHOLDS,
  bundle: BUNDLE_CONFIG,
  memory: MEMORY_CONFIG,
  api: API_CONFIG,
  database: DB_QUERY_CONFIG,
  assets: ASSET_CONFIG,
  subscriptions: SUBSCRIPTION_CONFIG,
  monitoring: MONITORING_CONFIG,
  cacheInvalidation: CACHE_INVALIDATION,
  queryKeys: QUERY_KEYS,
  utils: PERFORMANCE_UTILS,
} as const;
