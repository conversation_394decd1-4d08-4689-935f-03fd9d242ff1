import { useState, useEffect, useRef, useCallback, useMemo } from "react";
import { useAuth } from "@/components/auth/AuthProvider";
import { useNavigate } from "react-router-dom";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Clock, MapPin, User, Calendar, Activity } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { ThemeSwitcher } from "@/components/ThemeSwitcher";
import AOS from "aos";

const ClockInPage = () => {
  const { userProfile, isAuthenticated, loading, initialized } = useAuth();
  const navigate = useNavigate();
  const [currentTime, setCurrentTime] = useState(new Date());
  const [isAnimating, setIsAnimating] = useState(false);
  const aosInitialized = useRef(false);

  // Move all hooks to the top before any conditional logic
  const handleClockIn = useCallback(() => {
    setIsAnimating(true);
    setTimeout(() => {
      // Redirect to auth page for proper login flow
      navigate('/auth');
    }, 1500);
  }, [navigate]);

  // Memoize the formatted time to prevent unnecessary re-renders
  const formattedTime = useMemo(() => {
    return currentTime.toLocaleTimeString('en-US', {
      hour12: true,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  }, [currentTime]);

  const formattedDate = useMemo(() => {
    return currentTime.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }, [currentTime]);

  useEffect(() => {
    // Only initialize AOS once to prevent constant refreshing
    if (!aosInitialized.current) {
      console.log('Initializing AOS for ClockInPage');

      // Clear any existing AOS instances first
      if (typeof AOS.refresh === 'function') {
        AOS.refresh();
      }

      AOS.init({
        duration: 1200,
        easing: 'ease-out-cubic',
        once: true, // Only animate once to prevent constant refreshing
        mirror: false, // Disable mirror to prevent re-triggering on scroll
        disable: false,
        startEvent: 'DOMContentLoaded',
        initClassName: 'aos-init',
        animatedClassName: 'aos-animate',
        useClassNames: false,
        disableMutationObserver: false,
        debounceDelay: 50,
        throttleDelay: 99,
        offset: 120,
        delay: 0,
        anchorPlacement: 'top-bottom',
      });
      aosInitialized.current = true;
    }

    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => {
      clearInterval(timer);
    };
  }, []);

  // Add debugging to track auth state changes
  useEffect(() => {
    console.log('ClockInPage auth state:', {
      isAuthenticated,
      loading,
      initialized,
      userProfile: userProfile?.email
    });
  }, [isAuthenticated, loading, initialized, userProfile]);

  // Show loading state while auth is initializing
  if (loading || !initialized) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#ff1c04] mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="dashboard-container relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Gradient Orbs */}
        <div className="absolute -top-40 -right-40 w-96 h-96 bg-gradient-to-br from-[#ff1c04]/30 to-[#0FA0CE]/20 rounded-full blur-3xl animate-pulse floating-animation" data-aos="fade-in" data-aos-delay="500"></div>
        <div className="absolute -bottom-40 -left-40 w-96 h-96 bg-gradient-to-tr from-[#0FA0CE]/30 to-[#ff1c04]/20 rounded-full blur-3xl animate-pulse floating-animation" data-aos="fade-in" data-aos-delay="700" style={{ animationDelay: '1s' }}></div>
        
        {/* Geometric Patterns */}
        <div className="absolute top-20 left-10 w-32 h-32 border border-[#ff1c04]/20 rounded-full animate-spin" style={{ animationDuration: '20s' }}></div>
        <div className="absolute bottom-20 right-10 w-24 h-24 border border-[#0FA0CE]/20 rounded-full animate-spin" style={{ animationDuration: '15s', animationDirection: 'reverse' }}></div>
      </div>

      {/* Theme Switcher */}
      <div className="fixed top-6 right-6 z-50" data-aos="fade-left">
        <ThemeSwitcher />
      </div>

      {/* Main Content */}
      <div className="relative z-10 min-h-screen flex flex-col items-center justify-center px-4">
        {/* Logo Section */}
        <div className="mb-12" data-aos="zoom-in" data-aos-duration="1000">
          <div className="relative">
            <img 
              src="/lovable-uploads/491c7e61-a4fb-46a3-a002-904b84354e48.png" 
              alt="CTNL Logo" 
              className="h-20 w-auto mx-auto drop-shadow-2xl"
            />
            <div className="absolute inset-0 bg-gradient-to-r from-[#ff1c04]/20 to-[#0FA0CE]/20 blur-xl -z-10 animate-pulse"></div>
          </div>
        </div>

        {/* Main Clock-In Interface */}
        <div className="w-full max-w-lg">
          <Card className="glass-card border-0 shadow-2xl" data-aos="fade-up" data-aos-delay="200">
            <CardContent className="p-10 text-center">
              {/* 3D Circle Animation with Layers */}
              <div className="relative mb-10 flex items-center justify-center">
                <div className={`relative w-56 h-56 ${isAnimating ? 'animate-spin' : ''}`}>
                  {/* Outer Ring */}
                  <div className="absolute inset-0 rounded-full bg-gradient-to-r from-[#ff1c04]/30 via-[#0FA0CE]/20 to-[#ff1c04]/30 animate-pulse blur-sm"></div>
                  
                  {/* Middle Ring */}
                  <div className="absolute inset-3 rounded-full bg-gradient-to-r from-[#0FA0CE]/40 via-[#ff1c04]/30 to-[#0FA0CE]/40 animate-pulse" style={{ animationDelay: '0.5s' }}></div>
                  
                  {/* Inner Circle - Main Button */}
                  <div 
                    className="absolute inset-8 rounded-full bg-gradient-to-br from-[#ff1c04] via-[#e01703] to-[#cc1502] shadow-2xl hover:shadow-3xl cursor-pointer flex items-center justify-center transform hover:scale-110 transition-all duration-500 pulse-glow"
                    onClick={handleClockIn}
                  >
                    <div className="text-center text-white relative z-10">
                      <Clock className="h-10 w-10 mx-auto mb-3 drop-shadow-lg" />
                      <span className="text-xl font-bold tracking-wide">CLOCK IN</span>
                    </div>
                    
                    {/* Inner Glow Effect */}
                    <div className="absolute inset-2 rounded-full bg-gradient-to-br from-white/20 to-transparent"></div>
                  </div>
                  
                  {/* Rotating Border */}
                  <div className="absolute inset-0 rounded-full border-2 border-dashed border-[#ff1c04]/60 animate-spin" style={{ animationDuration: '12s' }}></div>
                  <div className="absolute inset-4 rounded-full border border-dashed border-[#0FA0CE]/40 animate-spin" style={{ animationDuration: '8s', animationDirection: 'reverse' }}></div>
                </div>
              </div>

              {/* Enhanced Time Display */}
              <div className="mb-8 p-6 glassmorphism rounded-2xl" data-aos="fade-up" data-aos-delay="400">
                <div className="text-4xl font-bold modern-heading mb-2">
                  {formattedTime}
                </div>
                <div className="text-muted-foreground text-lg font-medium">
                  {formattedDate}
                </div>
              </div>

              {/* Enhanced Quick Stats */}
              <div className="grid grid-cols-2 gap-4 mb-8" data-aos="fade-up" data-aos-delay="600">
                <div className="stats-card text-center">
                  <Calendar className="h-6 w-6 mx-auto mb-2 text-[#ff1c04]" />
                  <div className="text-sm font-semibold">Today</div>
                  <div className="text-xs text-muted-foreground">Ready to start</div>
                </div>
                <div className="stats-card text-center">
                  <MapPin className="h-6 w-6 mx-auto mb-2 text-[#0FA0CE]" />
                  <div className="text-sm font-semibold">Location</div>
                  <div className="text-xs text-muted-foreground">HQ Office</div>
                </div>
              </div>

              {/* Enhanced Action Button */}
              <Button 
                onClick={handleClockIn}
                className="modern-btn w-full text-lg py-4 rounded-2xl"
                data-aos="fade-up" 
                data-aos-delay="700"
              >
                Continue to Login
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Enhanced Footer */}
        <div className="mt-12 text-center" data-aos="fade-up" data-aos-delay="800">
          <div className="glassmorphism rounded-2xl p-6 inline-block">
            <h3 className="text-xl font-bold modern-heading mb-2">CTNL Work-Board</h3>
            <p className="text-muted-foreground">Professional Task Management System</p>
            <div className="flex items-center justify-center gap-4 mt-3 text-sm text-muted-foreground">
              <span>Secure • Reliable • Efficient</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ClockInPage;
