import { DatabaseTest } from '@/components/debug/DatabaseTest';
import { FrontendComponentTest } from '@/components/debug/FrontendComponentTest';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export const DebugPage = () => {
  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-8">System Debug & Testing</h1>

      <Tabs defaultValue="database" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="database">Database & API Testing</TabsTrigger>
          <TabsTrigger value="frontend">Frontend Component Testing</TabsTrigger>
        </TabsList>

        <TabsContent value="database">
          <DatabaseTest />
        </TabsContent>

        <TabsContent value="frontend">
          <FrontendComponentTest />
        </TabsContent>
      </Tabs>
    </div>
  );
};
