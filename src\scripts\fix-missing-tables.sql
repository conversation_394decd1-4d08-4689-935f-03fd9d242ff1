-- Fix missing tables: document_archive and project_assignments
-- This script creates the missing tables that should exist according to the schema

-- Create document_archive table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.document_archive (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    title TEXT NOT NULL,
    description TEXT,
    file_path TEXT NOT NULL,
    file_type TEXT NOT NULL,
    file_size BIGINT NOT NULL,
    uploaded_by UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    department_id UUID REFERENCES public.departments(id) ON DELETE SET NULL,
    tags TEXT[],
    is_archived BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Ensure project_assignments table exists with correct structure
CREATE TABLE IF NOT EXISTS public.project_assignments (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    project_name TEXT NOT NULL,
    description TEXT,
    assigned_to UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    department_id UUID REFERENCES public.departments(id) ON DELETE SET NULL,
    start_date TIMESTAMP WITH TIME ZONE NOT NULL,
    end_date TIMESTAMP WITH TIME ZONE,
    status TEXT CHECK (status IN ('pending', 'in_progress', 'completed', 'cancelled')) DEFAULT 'pending',
    priority TEXT CHECK (priority IN ('low', 'medium', 'high')) DEFAULT 'medium',
    progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage >= 0 AND progress_percentage <= 100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Enable RLS on both tables
ALTER TABLE public.document_archive ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_assignments ENABLE ROW LEVEL SECURITY;

-- Document archive policies
CREATE POLICY "document_archive_select" ON public.document_archive
    FOR SELECT TO authenticated USING (
        auth.uid() = uploaded_by OR
        department_id IN (
            SELECT department_id FROM public.profiles WHERE id = auth.uid()
        ) OR
        auth.uid() IN (
            SELECT id FROM public.profiles WHERE role IN ('admin', 'manager')
        )
    );

CREATE POLICY "document_archive_insert" ON public.document_archive
    FOR INSERT TO authenticated WITH CHECK (
        auth.uid() = uploaded_by
    );

CREATE POLICY "document_archive_update" ON public.document_archive
    FOR UPDATE TO authenticated USING (
        auth.uid() = uploaded_by OR
        auth.uid() IN (
            SELECT id FROM public.profiles WHERE role IN ('admin', 'manager')
        )
    );

-- Project assignments policies
CREATE POLICY "project_assignments_select" ON public.project_assignments
    FOR SELECT TO authenticated USING (
        auth.uid() = assigned_to OR
        auth.uid() IN (
            SELECT manager_id FROM public.departments WHERE id = department_id
        ) OR
        auth.uid() IN (
            SELECT id FROM public.profiles WHERE role IN ('admin', 'manager')
        )
    );

CREATE POLICY "project_assignments_insert" ON public.project_assignments
    FOR INSERT TO authenticated WITH CHECK (
        auth.uid() IN (
            SELECT id FROM public.profiles WHERE role IN ('admin', 'manager')
        )
    );

CREATE POLICY "project_assignments_update" ON public.project_assignments
    FOR UPDATE TO authenticated USING (
        auth.uid() = assigned_to OR
        auth.uid() IN (
            SELECT manager_id FROM public.departments WHERE id = department_id
        ) OR
        auth.uid() IN (
            SELECT id FROM public.profiles WHERE role IN ('admin', 'manager')
        )
    );

-- Create updated_at triggers
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_document_archive_updated_at
    BEFORE UPDATE ON public.document_archive
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_project_assignments_updated_at
    BEFORE UPDATE ON public.project_assignments
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add some sample data for testing
INSERT INTO public.document_archive (title, description, file_path, file_type, file_size, uploaded_by, department_id, tags) 
SELECT 
    'Sample Document',
    'Test document for system validation',
    '/documents/sample.pdf',
    'application/pdf',
    1024,
    p.id,
    d.id,
    ARRAY['test', 'sample']
FROM public.profiles p
CROSS JOIN public.departments d
WHERE p.role = 'admin'
LIMIT 1
ON CONFLICT DO NOTHING;

INSERT INTO public.project_assignments (project_name, description, assigned_to, department_id, start_date, status, priority)
SELECT 
    'System Health Check Project',
    'Automated project assignment for system testing',
    p.id,
    d.id,
    NOW(),
    'in_progress',
    'high'
FROM public.profiles p
CROSS JOIN public.departments d
WHERE p.role IN ('staff', 'manager')
LIMIT 1
ON CONFLICT DO NOTHING;
