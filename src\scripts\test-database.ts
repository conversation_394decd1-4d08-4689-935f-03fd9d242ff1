import { supabase } from '@/integrations/supabase/client';

const requiredTables = [
  'profiles',
  'tasks', 
  'projects',
  'memos',
  'departments',
  'api_keys',
  'roles',
  'system_activities',
  'document_archive',
  'project_assignments',
  'budgets',
  'expenses',
  'telecom_sites',
  'integrations',
  'integration_logs',
  'memo_notifications',
  'leave_requests',
  'email_notifications',
  'battery_management',
  'leave_balances',
  'notifications',
  'role_dashboard_configs'
];

export async function testDatabaseConnection() {
  console.log('🔍 Testing database connection...');
  
  try {
    // Test basic connection
    const { data: testData, error: testError } = await supabase
      .from('profiles')
      .select('id')
      .limit(1);
    
    if (testError) {
      console.error('❌ Database connection failed:', testError.message);
      return false;
    }
    
    console.log('✅ Database connection successful');
    return true;
  } catch (error) {
    console.error('❌ Database connection exception:', error);
    return false;
  }
}

export async function testTableStructure() {
  console.log('\n🔍 Testing table structure...');
  
  const existingTables: string[] = [];
  const missingTables: string[] = [];
  const tableErrors: { table: string; error: string }[] = [];
  
  for (const tableName of requiredTables) {
    try {
      const { data, error } = await supabase
        .from(tableName)
        .select('*')
        .limit(1);
      
      if (!error) {
        existingTables.push(tableName);
        console.log(`✅ ${tableName} - OK`);
      } else {
        missingTables.push(tableName);
        tableErrors.push({ table: tableName, error: error.message });
        console.log(`❌ ${tableName} - ERROR: ${error.message}`);
      }
    } catch (err: any) {
      missingTables.push(tableName);
      tableErrors.push({ table: tableName, error: err.message });
      console.log(`❌ ${tableName} - EXCEPTION: ${err.message}`);
    }
  }
  
  console.log(`\n📊 Table Structure Summary:`);
  console.log(`  Tables found: ${existingTables.length}/${requiredTables.length}`);
  
  if (missingTables.length > 0) {
    console.log(`  Missing tables: ${missingTables.join(', ')}`);
  }
  
  return { existingTables, missingTables, tableErrors };
}

export async function testUserProfiles() {
  console.log('\n👥 Testing user profiles...');
  
  try {
    const { data: profiles, error } = await supabase
      .from('profiles')
      .select('id, full_name, email, role, department_id')
      .limit(5);
    
    if (error) {
      console.error('❌ Failed to fetch profiles:', error.message);
      return false;
    }
    
    console.log(`✅ Found ${profiles?.length || 0} user profiles`);
    
    if (profiles && profiles.length > 0) {
      console.log('Sample profiles:');
      profiles.forEach((profile, index) => {
        console.log(`  ${index + 1}. ${profile.full_name || 'No name'} (${profile.role || 'No role'})`);
      });
    }
    
    return true;
  } catch (error: any) {
    console.error('❌ Profile test exception:', error.message);
    return false;
  }
}

export async function testDepartments() {
  console.log('\n🏢 Testing departments...');
  
  try {
    const { data: departments, error } = await supabase
      .from('departments')
      .select('id, name, description, manager_id')
      .limit(10);
    
    if (error) {
      console.error('❌ Failed to fetch departments:', error.message);
      return false;
    }
    
    console.log(`✅ Found ${departments?.length || 0} departments`);
    
    if (departments && departments.length > 0) {
      console.log('Departments:');
      departments.forEach((dept, index) => {
        console.log(`  ${index + 1}. ${dept.name} - ${dept.description || 'No description'}`);
      });
    }
    
    return true;
  } catch (error: any) {
    console.error('❌ Department test exception:', error.message);
    return false;
  }
}

export async function runDatabaseDiagnostic() {
  console.log('🚀 Starting Database Diagnostic...\n');
  
  const results = {
    connection: false,
    tableStructure: { existingTables: [], missingTables: [], tableErrors: [] },
    profiles: false,
    departments: false
  };
  
  try {
    results.connection = await testDatabaseConnection();
    results.tableStructure = await testTableStructure();
    results.profiles = await testUserProfiles();
    results.departments = await testDepartments();
    
    console.log('\n📋 DATABASE DIAGNOSTIC REPORT:');
    console.log('================================');
    
    console.log(`Database Connection: ${results.connection ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Table Structure: ${results.tableStructure.missingTables.length === 0 ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`User Profiles: ${results.profiles ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`Departments: ${results.departments ? '✅ PASS' : '❌ FAIL'}`);
    
    const overallHealth = results.connection && 
                         results.tableStructure.missingTables.length === 0 && 
                         results.profiles && 
                         results.departments;
    
    console.log(`\nOverall Database Health: ${overallHealth ? '🎉 HEALTHY' : '⚠️ NEEDS ATTENTION'}`);
    
    if (!overallHealth) {
      console.log('\n🔧 Recommendations:');
      if (!results.connection) {
        console.log('  - Check Supabase connection settings');
      }
      if (results.tableStructure.missingTables.length > 0) {
        console.log('  - Run database migrations to create missing tables');
        console.log('  - Check supabase/migrations/ directory');
      }
      if (!results.profiles) {
        console.log('  - Verify profiles table structure and permissions');
      }
      if (!results.departments) {
        console.log('  - Verify departments table structure and permissions');
      }
    }
    
    return results;
    
  } catch (error: any) {
    console.error('💥 Database diagnostic failed:', error.message);
    return results;
  }
}

// Export for use in other modules
export default {
  testDatabaseConnection,
  testTableStructure,
  testUserProfiles,
  testDepartments,
  runDatabaseDiagnostic
};
