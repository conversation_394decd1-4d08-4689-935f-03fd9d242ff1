-- Performance Optimization: Database Indexes
-- This migration creates indexes to improve query performance across the application

-- ============================================================================
-- TASK PERFORMANCE INDEXES
-- ============================================================================

-- Index for task queries by assignee and status (most common query pattern)
CREATE INDEX IF NOT EXISTS idx_tasks_assigned_to_status 
ON public.tasks(assigned_to_id, status) 
WHERE assigned_to_id IS NOT NULL;

-- Index for task queries by project and status
CREATE INDEX IF NOT EXISTS idx_tasks_project_status 
ON public.tasks(project_id, status) 
WHERE project_id IS NOT NULL;

-- Index for task queries by creation date (for recent tasks)
CREATE INDEX IF NOT EXISTS idx_tasks_created_at 
ON public.tasks(created_at DESC);

-- Index for task queries by due date (for upcoming tasks)
CREATE INDEX IF NOT EXISTS idx_tasks_due_date 
ON public.tasks(due_date) 
WHERE due_date IS NOT NULL;

-- Index for task queries by priority
CREATE INDEX IF NOT EXISTS idx_tasks_priority 
ON public.tasks(priority) 
WHERE priority IS NOT NULL;

-- ============================================================================
-- PROJECT PERFORMANCE INDEXES
-- ============================================================================

-- Index for project queries by manager and status
CREATE INDEX IF NOT EXISTS idx_projects_manager_status 
ON public.projects(manager_id, status) 
WHERE manager_id IS NOT NULL;

-- Index for project queries by department
CREATE INDEX IF NOT EXISTS idx_projects_department 
ON public.projects(department_id) 
WHERE department_id IS NOT NULL;

-- Index for project queries by creation date
CREATE INDEX IF NOT EXISTS idx_projects_created_at 
ON public.projects(created_at DESC);

-- Index for project queries by budget range
CREATE INDEX IF NOT EXISTS idx_projects_budget 
ON public.projects(budget) 
WHERE budget IS NOT NULL;

-- ============================================================================
-- USER AND PROFILE INDEXES
-- ============================================================================

-- Index for profile queries by department and role (common for dashboard queries)
CREATE INDEX IF NOT EXISTS idx_profiles_department_role 
ON public.profiles(department_id, role) 
WHERE department_id IS NOT NULL;

-- Index for profile queries by role only
CREATE INDEX IF NOT EXISTS idx_profiles_role 
ON public.profiles(role);

-- Index for profile queries by email (for user lookup)
CREATE INDEX IF NOT EXISTS idx_profiles_email 
ON public.profiles(email);

-- Index for profile queries by full name (for search)
CREATE INDEX IF NOT EXISTS idx_profiles_full_name 
ON public.profiles(full_name);

-- ============================================================================
-- SYSTEM ACTIVITY INDEXES
-- ============================================================================

-- Index for activity queries by user and date (most common pattern)
CREATE INDEX IF NOT EXISTS idx_system_activities_user_date 
ON public.system_activities(user_id, created_at DESC) 
WHERE user_id IS NOT NULL;

-- Index for activity queries by action type
CREATE INDEX IF NOT EXISTS idx_system_activities_action 
ON public.system_activities(action);

-- Index for activity queries by table name
CREATE INDEX IF NOT EXISTS idx_system_activities_table 
ON public.system_activities(table_name);

-- ============================================================================
-- NOTIFICATION INDEXES
-- ============================================================================

-- Index for notification queries by user and read status
CREATE INDEX IF NOT EXISTS idx_notifications_user_read 
ON public.notifications(user_id, is_read, created_at DESC) 
WHERE user_id IS NOT NULL;

-- Index for notification queries by type
CREATE INDEX IF NOT EXISTS idx_notifications_type 
ON public.notifications(type);

-- ============================================================================
-- MEMO INDEXES
-- ============================================================================

-- Index for memo queries by department and date
CREATE INDEX IF NOT EXISTS idx_memos_department_date 
ON public.memos(department_id, created_at DESC) 
WHERE department_id IS NOT NULL;

-- Index for memo queries by author
CREATE INDEX IF NOT EXISTS idx_memos_author 
ON public.memos(author_id) 
WHERE author_id IS NOT NULL;

-- Index for memo queries by priority
CREATE INDEX IF NOT EXISTS idx_memos_priority 
ON public.memos(priority) 
WHERE priority IS NOT NULL;

-- ============================================================================
-- DOCUMENT ARCHIVE INDEXES
-- ============================================================================

-- Index for document queries by category and date
CREATE INDEX IF NOT EXISTS idx_documents_category_date 
ON public.document_archive(category, created_at DESC) 
WHERE category IS NOT NULL;

-- Index for document queries by uploader
CREATE INDEX IF NOT EXISTS idx_documents_uploader 
ON public.document_archive(uploaded_by) 
WHERE uploaded_by IS NOT NULL;

-- Index for document queries by file type
CREATE INDEX IF NOT EXISTS idx_documents_file_type 
ON public.document_archive(file_type) 
WHERE file_type IS NOT NULL;

-- Full-text search index for document titles and descriptions
CREATE INDEX IF NOT EXISTS idx_documents_search 
ON public.document_archive USING gin(to_tsvector('english', title || ' ' || COALESCE(description, '')));

-- ============================================================================
-- FINANCIAL INDEXES
-- ============================================================================

-- Index for expense queries by project and date
CREATE INDEX IF NOT EXISTS idx_expenses_project_date 
ON public.expenses(project_id, expense_date DESC) 
WHERE project_id IS NOT NULL;

-- Index for expense queries by category
CREATE INDEX IF NOT EXISTS idx_expenses_category 
ON public.expenses(category) 
WHERE category IS NOT NULL;

-- Index for budget queries by department
CREATE INDEX IF NOT EXISTS idx_budgets_department 
ON public.budgets(department_id) 
WHERE department_id IS NOT NULL;

-- ============================================================================
-- INTEGRATION INDEXES
-- ============================================================================

-- Index for integration log queries by integration and date
CREATE INDEX IF NOT EXISTS idx_integration_logs_integration_date 
ON public.integration_logs(integration_id, created_at DESC) 
WHERE integration_id IS NOT NULL;

-- Index for integration log queries by status
CREATE INDEX IF NOT EXISTS idx_integration_logs_status 
ON public.integration_logs(status);

-- Index for API key queries by service provider
CREATE INDEX IF NOT EXISTS idx_api_keys_provider 
ON public.api_keys(service_provider);

-- ============================================================================
-- LEAVE MANAGEMENT INDEXES
-- ============================================================================

-- Index for leave request queries by user and status
CREATE INDEX IF NOT EXISTS idx_leave_requests_user_status 
ON public.leave_requests(user_id, status) 
WHERE user_id IS NOT NULL;

-- Index for leave request queries by date range
CREATE INDEX IF NOT EXISTS idx_leave_requests_dates 
ON public.leave_requests(start_date, end_date);

-- Index for leave balance queries by user and year
CREATE INDEX IF NOT EXISTS idx_leave_balances_user_year 
ON public.leave_balances(user_id, year) 
WHERE user_id IS NOT NULL;

-- ============================================================================
-- COMPOSITE INDEXES FOR COMPLEX QUERIES
-- ============================================================================

-- Composite index for dashboard task queries (user, status, project)
CREATE INDEX IF NOT EXISTS idx_tasks_dashboard_composite 
ON public.tasks(assigned_to_id, status, project_id, created_at DESC) 
WHERE assigned_to_id IS NOT NULL;

-- Composite index for project management queries (manager, status, department)
CREATE INDEX IF NOT EXISTS idx_projects_management_composite 
ON public.projects(manager_id, status, department_id, created_at DESC) 
WHERE manager_id IS NOT NULL;

-- Composite index for notification dashboard (user, read status, type, date)
CREATE INDEX IF NOT EXISTS idx_notifications_dashboard_composite 
ON public.notifications(user_id, is_read, type, created_at DESC) 
WHERE user_id IS NOT NULL;

-- ============================================================================
-- PARTIAL INDEXES FOR SPECIFIC USE CASES
-- ============================================================================

-- Index for active projects only
CREATE INDEX IF NOT EXISTS idx_projects_active 
ON public.projects(manager_id, created_at DESC) 
WHERE status = 'active';

-- Index for pending tasks only
CREATE INDEX IF NOT EXISTS idx_tasks_pending 
ON public.tasks(assigned_to_id, created_at DESC) 
WHERE status = 'pending';

-- Index for unread notifications only
CREATE INDEX IF NOT EXISTS idx_notifications_unread 
ON public.notifications(user_id, created_at DESC) 
WHERE is_read = false;

-- ============================================================================
-- PERFORMANCE MONITORING
-- ============================================================================

-- Create a view to monitor index usage
CREATE OR REPLACE VIEW public.index_usage_stats AS
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_tup_read,
    idx_tup_fetch,
    idx_scan
FROM pg_stat_user_indexes 
WHERE schemaname = 'public'
ORDER BY idx_scan DESC;

-- Grant access to the view
GRANT SELECT ON public.index_usage_stats TO authenticated;

-- ============================================================================
-- COMMENTS FOR DOCUMENTATION
-- ============================================================================

COMMENT ON INDEX idx_tasks_assigned_to_status IS 'Optimizes task queries by assignee and status - primary dashboard query';
COMMENT ON INDEX idx_projects_manager_status IS 'Optimizes project queries by manager and status - manager dashboard';
COMMENT ON INDEX idx_system_activities_user_date IS 'Optimizes activity log queries by user and date - activity feeds';
COMMENT ON INDEX idx_notifications_user_read IS 'Optimizes notification queries by user and read status - notification center';
COMMENT ON INDEX idx_documents_search IS 'Full-text search optimization for document titles and descriptions';

-- ============================================================================
-- ANALYZE TABLES FOR UPDATED STATISTICS
-- ============================================================================

-- Update table statistics for the query planner
ANALYZE public.tasks;
ANALYZE public.projects;
ANALYZE public.profiles;
ANALYZE public.system_activities;
ANALYZE public.notifications;
ANALYZE public.memos;
ANALYZE public.document_archive;
ANALYZE public.expenses;
ANALYZE public.budgets;
ANALYZE public.integration_logs;
ANALYZE public.leave_requests;
ANALYZE public.leave_balances;
