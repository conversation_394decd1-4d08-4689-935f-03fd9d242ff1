-- Fix time_logs table for clock-in/clock-out functionality
-- This migration ensures the time_logs table exists with correct schema

-- Drop existing table if it has wrong structure
DROP TABLE IF EXISTS public.clock_in_records CASCADE;

-- Create the time_logs table with correct structure
CREATE TABLE IF NOT EXISTS public.time_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    clock_in TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    clock_out TIMESTAMP WITH TIME ZONE,
    clock_in_timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    clock_out_timestamp TIMESTAMP WITH TIME ZONE,
    total_hours DECIMAL(5,2),
    latitude DECIMAL(10, 8),
    longitude DECIMAL(11, 8),
    location_address TEXT,
    location_latitude DECIMAL(10, 8),
    location_longitude DECIMAL(11, 8),
    date DATE DEFAULT CURRENT_DATE,
    project_id UUID REFERENCES public.projects(id) ON DELETE SET NULL,
    task_id UUID REFERENCES public.tasks(id) ON DELETE SET NULL,
    notes TEXT,
    device_info JSONB,
    timezone TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_time_logs_user_id ON public.time_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_time_logs_clock_in ON public.time_logs(clock_in);
CREATE INDEX IF NOT EXISTS idx_time_logs_date ON public.time_logs(date);
CREATE INDEX IF NOT EXISTS idx_time_logs_user_date ON public.time_logs(user_id, date);

-- Enable Row Level Security
ALTER TABLE public.time_logs ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own time logs" ON public.time_logs;
DROP POLICY IF EXISTS "Users can insert their own time logs" ON public.time_logs;
DROP POLICY IF EXISTS "Users can update their own time logs" ON public.time_logs;
DROP POLICY IF EXISTS "Managers can view all time logs" ON public.time_logs;

-- Create RLS policies
CREATE POLICY "Users can view their own time logs" ON public.time_logs
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own time logs" ON public.time_logs
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own time logs" ON public.time_logs
    FOR UPDATE USING (auth.uid() = user_id);

-- Allow managers and admins to view all time logs
CREATE POLICY "Managers can view all time logs" ON public.time_logs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.profiles 
            WHERE profiles.id = auth.uid() 
            AND profiles.role IN ('manager', 'admin')
        )
    );

-- Create a function to automatically update the updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Drop existing trigger if it exists
DROP TRIGGER IF EXISTS update_time_logs_updated_at ON public.time_logs;

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_time_logs_updated_at
    BEFORE UPDATE ON public.time_logs
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create the missing get_realtime_clock_status function
-- This function provides real-time clock status for all users
CREATE OR REPLACE FUNCTION public.get_realtime_clock_status()
RETURNS TABLE (
    user_id UUID,
    user_name TEXT,
    department_name TEXT,
    is_clocked_in BOOLEAN,
    clock_in_time TIMESTAMP WITH TIME ZONE,
    duration_hours NUMERIC,
    location_address TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        p.id as user_id,
        p.full_name as user_name,
        COALESCE(d.name, 'No Department') as department_name,
        (tl.id IS NOT NULL AND tl.clock_out IS NULL) as is_clocked_in,
        tl.clock_in as clock_in_time,
        CASE
            WHEN tl.clock_out IS NULL AND tl.clock_in IS NOT NULL THEN
                EXTRACT(EPOCH FROM (NOW() - tl.clock_in)) / 3600.0
            ELSE 0
        END as duration_hours,
        COALESCE(tl.location_address, 'Unknown') as location_address
    FROM public.profiles p
    LEFT JOIN public.departments d ON p.department_id = d.id
    LEFT JOIN LATERAL (
        SELECT id, clock_in, clock_out, location_address
        FROM public.time_logs tl_inner
        WHERE tl_inner.user_id = p.id
        AND tl_inner.clock_out IS NULL
        ORDER BY tl_inner.clock_in DESC
        LIMIT 1
    ) tl ON true
    WHERE p.role IN ('staff', 'manager', 'admin', 'staff-admin', 'accountant')
    ORDER BY p.full_name;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions on the function
GRANT EXECUTE ON FUNCTION public.get_realtime_clock_status TO authenticated;
